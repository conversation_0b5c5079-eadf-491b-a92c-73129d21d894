"""General Acquisition Module Validator - DICOM PS3.3 C.7.10.1

This validator provides comprehensive validation for the General Acquisition Module,
ensuring compliance with DICOM PS3.3 C.7.10.1 specifications.

Key validation areas:
- UID format compliance (Acquisition UID, Irradiation Event UID)
- Date/time format validation (DA, TM, DT formats)
- Numeric range validation (duration, image counts)
- Logical consistency between related attributes
- VM 1-n support for Irradiation Event UID

All elements in this module are Type 3 (optional), so validation focuses on
format correctness and semantic consistency when elements are present.
"""

from datetime import datetime
import re
from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class GeneralAcquisitionValidator(BaseValidator):
    """Validator for General Acquisition Module (C.7.10.1).

    Validates all aspects of the General Acquisition Module including:
    - UID format compliance for single and multiple values
    - Date/time format validation with semantic checks
    - Numeric range validation with practical limits
    - Logical consistency between acquisition timing attributes
    - Irradiation event context guidance per DICOM PS3.3 C.********.1
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Note: All elements in General Acquisition Module are Type 3 (optional),
        so this method returns an empty ValidationResult with no errors.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no required elements in this module)
        """
        result = ValidationResult()
        # All elements in General Acquisition Module are Type 3 (optional)
        # No required elements to validate
        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Note: All elements in General Acquisition Module are Type 3 (optional),
        so this method returns an empty ValidationResult with no conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no conditional requirements in this module)
        """
        result = ValidationResult()
        # All elements in General Acquisition Module are Type 3 (optional)
        # No conditional requirements to validate
        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Note: General Acquisition Module has no enumerated values to validate.
        All values are free-form (UIDs, dates, times, numbers).

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no enumerated values in this module)
        """
        result = ValidationResult()
        # General Acquisition Module has no enumerated values
        # All values are free-form (UIDs, dates, times, numbers)
        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Note: General Acquisition Module has no sequence structures to validate.
        All elements are simple data types (UI, DA, TM, DT, FD, IS).

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no sequences in this module)
        """
        result = ValidationResult()
        # General Acquisition Module has no sequence structures
        # All elements are simple data types
        return result

    @staticmethod
    def validate_uid_formats(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate UID format compliance per DICOM PS3.3 specifications.

        Validates:
        - Acquisition UID (0008,0017) - Type 3, UI VR
        - Irradiation Event UID (0008,3010) - Type 3, UI VR, VM 1-n

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid UID formats
        """
        result = ValidationResult()

        # Acquisition UID (0008,0017) - Type 3
        if 'AcquisitionUID' in data:
            acquisition_uid = data.AcquisitionUID
            GeneralAcquisitionValidator._validate_single_uid(
                acquisition_uid, "Acquisition UID (0008,0017)", result
            )

        # Irradiation Event UID (0008,3010) - Type 3, VM 1-n
        if 'IrradiationEventUID' in data:
            irradiation_uid = data.IrradiationEventUID
            # Handle VM 1-n: can be single string, list, or pydicom MultiValue
            try:
                # Check if it's iterable (list, MultiValue) but not a string
                if hasattr(irradiation_uid, '__iter__') and not isinstance(irradiation_uid, str):
                    # Multiple UIDs case (list or MultiValue)
                    if len(irradiation_uid) == 0:
                        result.add_error(
                            "Irradiation Event UID (0008,3010) list cannot be empty. "
                            "Per DICOM VM 1-n requirement, at least one UID must be provided when attribute is present."
                        )
                    else:
                        for i, uid in enumerate(irradiation_uid):
                            GeneralAcquisitionValidator._validate_single_uid(
                                uid, f"Irradiation Event UID[{i}] (0008,3010)", result
                            )
                else:
                    # Single UID case
                    GeneralAcquisitionValidator._validate_single_uid(
                        irradiation_uid, "Irradiation Event UID (0008,3010)", result
                    )
            except Exception as e:
                # Fallback: treat as single UID and provide helpful error context
                result.add_warning(
                    f"Irradiation Event UID (0008,3010) format could not be determined: {str(e)}. "
                    f"Treating as single UID for validation."
                )
                GeneralAcquisitionValidator._validate_single_uid(
                    str(irradiation_uid), "Irradiation Event UID (0008,3010)", result
                )

        return result

    @staticmethod
    def validate_datetime_formats(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate date and time format compliance per DICOM PS3.5.

        Validates:
        - Acquisition Date (0008,0022) - Type 3, DA VR (YYYYMMDD format)
        - Acquisition Time (0008,0032) - Type 3, TM VR (HHMMSS.FFFFFF format)
        - Acquisition DateTime (0008,002A) - Type 3, DT VR (YYYYMMDDHHMMSS.FFFFFF format)

        Performs both format and semantic validation including range checks.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid date/time formats
        """
        result = ValidationResult()

        # Date format: YYYYMMDD
        date_pattern = re.compile(r'^\d{8}$')

        # Time format: HHMMSS or HHMMSS.FFFFFF
        time_pattern = re.compile(r'^\d{6}(\.\d{1,6})?$')

        # DateTime format: YYYYMMDDHHMMSS.FFFFFF
        datetime_pattern = re.compile(r'^\d{14}(\.\d{1,6})?$')

        # Acquisition Date (0008,0022)
        if 'AcquisitionDate' in data:
            acquisition_date = data.AcquisitionDate
            if not date_pattern.match(acquisition_date):
                result.add_error(
                    f"Acquisition Date (0008,0022) format invalid: '{acquisition_date}'. "
                    f"Must be YYYYMMDD format"
                )
            else:
                # Validate actual date values
                try:
                    year = int(acquisition_date[:4])
                    month = int(acquisition_date[4:6])
                    day = int(acquisition_date[6:8])

                    if year < 2000 or year > datetime.now().year:
                        result.add_warning(
                            f"Acquisition Date (0008,0022) year outside of reasonable range: {year}"
                        )
                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid day: {day}"
                        )
                except ValueError:
                    result.add_error(
                        f"Acquisition Date (0008,0022) contains invalid numeric values: '{acquisition_date}'"
                    )

        # Acquisition Time (0008,0032)
        if 'AcquisitionTime' in data:
            acquisition_time = data.AcquisitionTime
            if not time_pattern.match(acquisition_time):
                result.add_error(
                    f"Acquisition Time (0008,0032) format invalid: '{acquisition_time}'. "
                    f"Must be HHMMSS or HHMMSS.FFFFFF format"
                )
            else:
                # Validate actual time values
                try:
                    time_part = acquisition_time.split('.')[0]
                    hour = int(time_part[:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])

                    if hour > 23:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition Time (0008,0032) contains invalid numeric values: '{acquisition_time}'"
                    )

        # Acquisition DateTime (0008,002A)
        if 'AcquisitionDateTime' in data:
            acquisition_datetime = data.AcquisitionDateTime
            if not datetime_pattern.match(acquisition_datetime):
                result.add_error(
                    f"Acquisition DateTime (0008,002A) format invalid: '{acquisition_datetime}'. "
                    f"Must be YYYYMMDDHHMMSS.FFFFFF format"
                )
            else:
                # Validate actual datetime values
                try:
                    datetime_part = acquisition_datetime.split('.')[0]
                    year = int(datetime_part[:4])
                    month = int(datetime_part[4:6])
                    day = int(datetime_part[6:8])
                    hour = int(datetime_part[8:10])
                    minute = int(datetime_part[10:12])
                    second = int(datetime_part[12:14])

                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid day: {day}"
                        )
                    if hour > 23:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition DateTime (0008,002A) contains invalid numeric values: '{acquisition_datetime}'"
                    )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations for General Acquisition Module.

        Performs comprehensive validation of all General Acquisition Module elements
        per DICOM PS3.3 C.7.10.1. Since all elements are Type 3 (optional), validation
        focuses on format correctness and semantic consistency when elements are present.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid elements
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Early return if data is None or invalid
        if data is None:
            result.add_error("Data cannot be None for General Acquisition Module validation")
            return result

        # Always validate required elements (empty for this module)
        result.merge(GeneralAcquisitionValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(GeneralAcquisitionValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(GeneralAcquisitionValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(GeneralAcquisitionValidator.validate_sequence_structures(data))

        # Always validate UID formats when present
        result.merge(GeneralAcquisitionValidator.validate_uid_formats(data))

        # Always validate date/time formats when present
        result.merge(GeneralAcquisitionValidator.validate_datetime_formats(data))

        # Always validate numeric ranges when present
        result.merge(GeneralAcquisitionValidator.validate_numeric_ranges(data))

        # Always validate logical consistency when relevant elements present
        result.merge(GeneralAcquisitionValidator.validate_logical_consistency(data))

        return result
    
    @staticmethod
    def validate_numeric_ranges(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate numeric value ranges and semantic constraints.

        Validates:
        - Acquisition Duration (0018,9073) - Type 3, FD VR (positive values expected)
        - Images in Acquisition (0020,1002) - Type 3, IS VR (positive integer values expected)

        Provides warnings for edge cases and errors for invalid numeric types.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid numeric values
        """
        result = ValidationResult()

        # Acquisition Duration (0018,9073) - should be positive
        # Check both normal attribute access and direct __dict__ access (for test scenarios)
        duration = None
        if 'AcquisitionDuration' in data:
            duration = data.AcquisitionDuration
        elif hasattr(data, '__dict__') and 'AcquisitionDuration' in data.__dict__:
            duration = data.__dict__['AcquisitionDuration']

        if duration is not None:
            try:
                duration_float = float(duration)
                if duration_float < 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) should be positive: {duration_float} seconds"
                    )
                elif duration_float == 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) is zero, which may indicate no actual acquisition time"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Acquisition Duration (0018,9073) must be a numeric value: '{duration}'"
                )

        # Images in Acquisition (0020,1002) - should be positive integer
        # Check both normal attribute access and direct __dict__ access (for test scenarios)
        images_count = None
        if 'ImagesInAcquisition' in data:
            images_count = data.ImagesInAcquisition
        elif hasattr(data, '__dict__') and 'ImagesInAcquisition' in data.__dict__:
            images_count = data.__dict__['ImagesInAcquisition']

        if images_count is not None:
            try:
                images_int = int(images_count)
                if images_int < 0:
                    result.add_error(
                        f"Images in Acquisition (0020,1002) cannot be negative: {images_int}"
                    )
                elif images_int == 0:
                    result.add_warning(
                        f"Images in Acquisition (0020,1002) is zero, which may indicate no images were acquired"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Images in Acquisition (0020,1002) must be an integer value: '{images_count}'"
                )

        return result

    @staticmethod
    def validate_logical_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate logical consistency between related acquisition attributes.

        Checks consistency between:
        - Acquisition Date (0008,0022) and Acquisition Time (0008,0032) vs Acquisition DateTime (0008,002A)
        - Provides guidance about complete timing information
        - Validates irradiation event context per DICOM PS3.3 C.********.1

        Focuses on user guidance for proper acquisition timing documentation.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with warnings for logical inconsistencies
        """
        result = ValidationResult()

        acquisition_date = data.AcquisitionDate if 'AcquisitionDate' in data else ''
        acquisition_time = data.AcquisitionTime if 'AcquisitionTime' in data else ''
        acquisition_datetime = data.AcquisitionDateTime if 'AcquisitionDateTime' in data else ''

        # If both date/time and datetime are present, they should be consistent
        if acquisition_date and acquisition_time and acquisition_datetime:
            expected_datetime = acquisition_date + acquisition_time
            # Remove fractional seconds for comparison if present
            datetime_base = acquisition_datetime.split('.')[0]
            expected_base = expected_datetime.split('.')[0]

            if datetime_base != expected_base:
                result.add_warning(
                    f"Acquisition DateTime (0008,002A) '{acquisition_datetime}' is inconsistent "
                    f"with Acquisition Date '{acquisition_date}' and Time '{acquisition_time}'"
                )

        # Warn if only one of date or time is present
        if acquisition_date and not acquisition_time:
            result.add_warning(
                "Acquisition Date (0008,0022) is present but Acquisition Time (0008,0032) is missing. "
                "Consider providing both for complete timing information"
            )

        if acquisition_time and not acquisition_date:
            result.add_warning(
                "Acquisition Time (0008,0032) is present but Acquisition Date (0008,0022) is missing. "
                "Consider providing both for complete timing information"
            )

        # Validate irradiation event context
        result.merge(GeneralAcquisitionValidator._validate_irradiation_event_context(data))

        return result
    
    @staticmethod
    def _validate_single_uid(uid: str, context: str, result: ValidationResult) -> None:
        """Validate a single UID format according to DICOM PS3.5 specifications.
        
        Validates UID format per DICOM PS3.5 Data Structures and Encoding:
        - Must contain only digits (0-9) and dots (.)
        - Maximum length 64 characters
        - Cannot start or end with dot
        - Cannot contain consecutive dots
        - Should be proper ISO OID format
        
        Args:
            uid: UID string to validate
            context: Context description for error messages (e.g., "Acquisition UID (0008,0017)")
            result: ValidationResult to add errors/warnings to
        """
        # UID format pattern: digits and dots only, per DICOM PS3.5
        uid_pattern = re.compile(r'^[0-9.]+$')
        
        if not uid_pattern.match(uid):
            result.add_error(
                f"{context} format invalid: '{uid}'. "
                f"Per DICOM PS3.5, UIDs must contain only digits (0-9) and dots (.). "
                f"Use a properly formatted ISO OID."
            )
        elif len(uid) > 64:
            result.add_error(
                f"{context} too long: {len(uid)} characters. "
                f"Per DICOM PS3.5, UIDs cannot exceed 64 characters. "
                f"Consider using a shorter UID or registered root."
            )
        elif uid.startswith('.') or uid.endswith('.'):
            result.add_error(
                f"{context} cannot start or end with dot: '{uid}'. "
                f"Per DICOM PS3.5, UIDs must start and end with digits."
            )
        elif '..' in uid:
            result.add_error(
                f"{context} cannot contain consecutive dots: '{uid}'. "
                f"Per DICOM PS3.5, each UID component must be separated by a single dot."
            )
        elif uid == '0':
            result.add_warning(
                f"{context} value '0' is not a valid UID root. "
                f"Per DICOM PS3.5, UIDs should be proper ISO OID format starting with a registered root."
            )
    



    @staticmethod
    def _validate_irradiation_event_context(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate irradiation event context and provide user guidance.

        Per DICOM PS3.3 C.********.1, irradiation events have complex relationships
        with acquisitions that require user understanding:

        - Not necessarily 1:1 relationship between irradiation event and acquisition
        - Single acquisition may result from multiple irradiation events
        - Multiple irradiation events may be involved in single acquisition
        - Acquisitions may not involve ionizing radiation (attribute absent)

        Provides contextual warnings to help users understand these relationships.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with warnings for irradiation event context
        """
        result = ValidationResult()

        if 'IrradiationEventUID' in data:
            irradiation_uid = data.IrradiationEventUID
            # Provide guidance about irradiation event relationships
            # Check if it's multiple values (list, MultiValue) and has more than one element
            try:
                if (hasattr(irradiation_uid, '__iter__') and
                    not isinstance(irradiation_uid, str) and
                    len(irradiation_uid) > 1):
                    result.add_warning(
                        f"Multiple Irradiation Event UIDs ({len(irradiation_uid)}) detected. "
                        f"Per DICOM PS3.3 C.********.1, this indicates either: "
                        f"(1) multiple X-ray sources, or (2) quiescent periods between irradiation events "
                        f"during which data gathering continued. Ensure this reflects the actual acquisition scenario."
                    )
            except Exception:
                # If we can't determine multiplicity, skip the warning
                pass

            # Only provide guidance for potentially confusing cases
            # (Multiple events need explanation, single events are straightforward)

        return result
