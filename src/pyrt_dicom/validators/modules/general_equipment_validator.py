"""General Equipment Module DICOM validation - PS3.3 C.7.5.1

This validator ensures complete compliance with DICOM PS3.3 C.7.5.1 General Equipment Module
requirements, including all Type 1C conditional logic, sequence validation, and semantic constraints.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class GeneralEquipmentValidator(BaseValidator):
    """Validator for DICOM General Equipment Module (PS3.3 C.7.5.1).

    Validates all requirements from DICOM PS3.3 C.7.5.1 including:
    - Type 2 required elements (Manufacturer)
    - Type 1C conditional requirements (Pixel Padding Value)
    - Sequence structure validation (Institutional Department Type Code, UDI)
    - Calibration date/time pairing requirements
    - Value representation constraints
    - Semantic validation of relationships between attributes

    This validator provides comprehensive error checking with clear, actionable
    error messages to guide users in creating DICOM-compliant datasets.

    All validation methods support both pydicom Dataset and BaseModule instances
    for flexible validation with zero-copy optimization.
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2 required elements.

        Type 2 elements are required to be present but may have empty values.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 2: Manufacturer (0008,0070) - Required but may be empty
        if 'Manufacturer' not in data:
            result.add_error(
                "Manufacturer (0008,0070) is required (Type 2). This element identifies the "
                "manufacturer of the equipment that produced the Composite Instances. "
                "It may be empty but must be present."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 1C: Pixel Padding Value (0028,0120) conditional requirement
        has_pixel_padding_range_limit = 'PixelPaddingRangeLimit' in data
        has_pixel_data = ('PixelData' in data or 'PixelDataProviderURL' in data)

        if has_pixel_padding_range_limit and has_pixel_data:
            if 'PixelPaddingValue' not in data:
                result.add_error(
                    "Pixel Padding Value (0028,0120) is required (Type 1C) when Pixel Padding Range Limit "
                    "(0028,0121) is present and either Pixel Data (7FE0,0010) or Pixel Data Provider URL "
                    "(0028,7FE0) is present. See DICOM PS3.3 C.*******.2 for details."
                )

        # Additional validation: Pixel Padding Value should only be present if pixel data exists
        if 'PixelPaddingValue' in data and not has_pixel_data:
            result.add_warning(
                "Pixel Padding Value (0028,0120) is present but no Pixel Data (7FE0,0010) or "
                "Pixel Data Provider URL (0028,7FE0) is found. Pixel Padding Value may only be "
                "present when pixel data is available."
            )

        # Validate pixel padding setup conditions
        GeneralEquipmentValidator._validate_pixel_padding_setup_conditions(data, result)

        # Validate calibration date/time pairing
        GeneralEquipmentValidator._validate_calibration_pairing(data, result)

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # No specific enumerated values defined for General Equipment Module
        # Most attributes are free text or numeric values
        # This method is included for consistency with the pattern

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Institutional Department Type Code Sequence (0008,1041) - Type 3
        if 'InstitutionalDepartmentTypeCodeSequence' in data:
            dept_type_seq = data.InstitutionalDepartmentTypeCodeSequence
            if dept_type_seq:
                # Only a single item is permitted in this sequence
                if len(dept_type_seq) > 1:
                    result.add_error(
                        "Institutional Department Type Code Sequence (0008,1041) may contain only a single item. "
                        f"Found {len(dept_type_seq)} items. See DICOM PS3.3 C.7.5.1."
                    )

                # Validate Code Sequence Macro attributes for each item
                for i, item in enumerate(dept_type_seq):
                    if 'CodeValue' not in item:
                        result.add_error(
                            f"Institutional Department Type Code Sequence item {i+1}: Code Value (0008,0100) is required "
                            "as part of Code Sequence Macro. See DICOM PS3.3 Table 8.8-1."
                        )
                    if 'CodingSchemeDesignator' not in item:
                        result.add_error(
                            f"Institutional Department Type Code Sequence item {i+1}: Coding Scheme Designator (0008,0102) "
                            "is required as part of Code Sequence Macro. See DICOM PS3.3 Table 8.8-1."
                        )

        # UDI Sequence (0018,100A) - Type 3
        if 'UDISequence' in data:
            udi_seq = data.UDISequence
            for i, item in enumerate(udi_seq):
                # UDI Macro validation (Table 10.29-1)
                if 'UniqueDeviceIdentifier' not in item:
                    result.add_error(
                        f"UDI Sequence item {i+1}: Unique Device Identifier is required as part of "
                        "UDI Macro. This is the complete UDI string that uniquely identifies the device. "
                        "See DICOM PS3.3 Table 10.29-1."
                    )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result containing comprehensive validation results
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(GeneralEquipmentValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(GeneralEquipmentValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(GeneralEquipmentValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(GeneralEquipmentValidator.validate_sequence_structures(data))

        # Validate semantic constraints and relationships
        if config.validate_semantic_constraints:
            result.merge(GeneralEquipmentValidator._validate_semantic_constraints(data))

        return result

    @staticmethod
    def _validate_pixel_padding_setup_conditions(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate pixel padding setup conditions that were previously in the module.

        This validation ensures that pixel padding value setup follows DICOM requirements
        without raising exceptions in the module itself.
        """
        # Check if attempting to set pixel padding value inappropriately
        if 'PixelPaddingValue' in data:
            has_pixel_padding_range_limit = 'PixelPaddingRangeLimit' in data
            has_pixel_data = ('PixelData' in data or 'PixelDataProviderURL' in data)

            # If Pixel Padding Range Limit is present, then Pixel Data must also be present
            if has_pixel_padding_range_limit and not has_pixel_data:
                result.add_error(
                    "Pixel Padding Value (0028,0120) cannot be set when Pixel Padding Range Limit "
                    "(0028,0121) is present but Pixel Data (7FE0,0010) or Pixel Data Provider URL "
                    "(0028,7FE0) is not present. This violates DICOM PS3.3 C.*******.2 requirements."
                )

    @staticmethod
    def _validate_calibration_pairing(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate calibration date/time pairing requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any errors
        """
        # Time of Last Calibration has no meaning unless Date of Last Calibration is also supported
        if 'TimeOfLastCalibration' in data and 'DateOfLastCalibration' not in data:
            result.add_error(
                "Time of Last Calibration (0018,1201) has no meaning unless "
                "Date of Last Calibration (0018,1200) is also supported. "
                "See DICOM PS3.3 C.*******.1."
            )

        # When both are present, they should have matching counts
        if 'DateOfLastCalibration' in data and 'TimeOfLastCalibration' in data:
            date_values = data.DateOfLastCalibration
            time_values = data.TimeOfLastCalibration

            # Convert single values to lists for consistent processing
            if isinstance(date_values, str):
                date_values = [date_values]
            if isinstance(time_values, str):
                time_values = [time_values]

            if len(date_values) != len(time_values):
                result.add_error(
                    f"Date of Last Calibration (0018,1200) and Time of Last Calibration (0018,1201) "
                    f"must have the same number of values when both are present. "
                    f"Found {len(date_values)} date values and {len(time_values)} time values. "
                    f"See DICOM PS3.3 C.*******.1."
                )


    @staticmethod
    def _validate_semantic_constraints(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate semantic constraints and relationships.

        Validates logical relationships and constraints that ensure the dataset
        makes semantic sense according to DICOM standards and real-world usage.
        """
        result = ValidationResult()

        # Validate Software Versions format and multiplicity
        if 'SoftwareVersions' in data:
            software_versions = data.SoftwareVersions
            if isinstance(software_versions, str):
                # Single value is fine
                pass
            elif isinstance(software_versions, list):
                if len(software_versions) == 0:
                    result.add_warning(
                        "Software Versions (0018,1020) is present but empty. If provided, it should "
                        "contain at least one software version identifier."
                    )
            else:
                result.add_warning(
                    "Software Versions (0018,1020) should be a string or list of strings representing "
                    "software version identifiers."
                )

        # Validate Device Class UID format
        if 'ManufacturerDeviceClassUID' in data:
            device_class_uids = data.ManufacturerDeviceClassUID
            if isinstance(device_class_uids, list):
                for uid in device_class_uids:
                    uid_str = str(uid)
                    # Check if UID contains only digits and periods
                    if not all(c.isdigit() or c == '.' for c in uid_str):
                        result.add_warning(
                            f"Manufacturer Device Class UID '{uid_str}' does not appear to be a valid UID format. "
                            "UIDs should contain only digits and periods."
                        )
            elif isinstance(device_class_uids, str):
                # Handle single UID
                if not all(c.isdigit() or c == '.' for c in device_class_uids):
                    result.add_warning(
                        f"Manufacturer Device Class UID '{device_class_uids}' does not appear to be a valid UID format. "
                        "UIDs should contain only digits and periods."
                    )

        # Validate Spatial Resolution is positive
        if 'SpatialResolution' in data:
            spatial_resolution = data.SpatialResolution
            if isinstance(spatial_resolution, (int, float)) and spatial_resolution < 0:
                result.add_warning(
                    f"Spatial Resolution (0018,1050) value {spatial_resolution} should be a positive value "
                    "representing the inherent limiting resolution in mm."
                )

        # Validate date formats
        date_fields = [
            ('DateOfManufacture', 'Date of Manufacture (0018,1204)'),
            ('DateOfInstallation', 'Date of Installation (0018,1205)'),
        ]

        for field_name, display_name in date_fields:
            if field_name in data:
                date_value = getattr(data, field_name)
                if isinstance(date_value, str) and len(date_value) == 10 and '-' in date_value:
                    result.add_warning(
                        f"{display_name} value '{date_value}' appears to be in YYYY-MM-DD format. "
                        "DICOM dates should be in DICOM DA format (YYYYMMDD) without separators."
                    )

        # Validate calibration date ordering
        if 'DateOfLastCalibration' in data:
            date_values = data.DateOfLastCalibration
            # Convert to list if it's not already
            if isinstance(date_values, str):
                date_values = [date_values]
            elif hasattr(date_values, '__iter__') and not isinstance(date_values, str):
                date_values = list(date_values)

            if len(date_values) > 1:
                # Check if dates are in chronological order (oldest to newest)
                for i in range(1, len(date_values)):
                    if str(date_values[i]) < str(date_values[i-1]):
                        result.add_warning(
                            "Date of Last Calibration (0018,1200) values should be ordered from oldest to most recent "
                            f"according to DICOM PS3.3 C.*******.1, but found {date_values[i-1]} before {date_values[i]}."
                        )
                        break

        return result


