"""General Image Module DICOM validation - PS3.3 C.7.6.1

Validates General Image Module attributes according to DICOM PS3.3 C.7.6.1 specification.
Provides comprehensive validation of Type 1, Type 2, Type 2C, and Type 3 elements
with detailed error messages and DICOM standard references.

This validator ensures 100% DICOM standard compliance for General Image Module
attributes and provides actionable error messages to guide users toward
compliant implementations.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from pydicom.multival import MultiValue
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod
)

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class GeneralImageValidator(BaseValidator):
    """Validator for DICOM General Image Module (PS3.3 C.7.6.1).

    Provides comprehensive validation of all General Image Module attributes
    according to DICOM PS3.3 C.7.6.1 specification. This validator ensures
    100% compliance with DICOM standards and provides actionable error messages
    to guide users toward compliant implementations.

    Validation Coverage:
    - Type 2 elements: Instance Number (required, may be empty)
    - Type 2C elements: Patient Orientation, Content Date/Time (conditional requirements)
    - Type 3 elements: All optional attributes with enumerated value checking
    - Sequence validation: Icon Image Sequence, Real World Value Mapping Sequence
    - Cross-field validation: Lossy compression attribute consistency
    - Format validation: Image Type structure, Patient Orientation format
    - Conditional logic: Spatial orientation requirements, temporal consistency

    Error Message Standards:
    - Include DICOM tag references (e.g., "(0020,0013)")
    - Reference DICOM PS3.3 sections for complex requirements
    - Provide actionable guidance for resolving issues
    - Explain the clinical/technical context of requirements
    - Suggest valid values for enumerated attributes

    Usage:
        # Basic validation
        result = GeneralImageValidator.validate(dataset)
        
        # Validation with custom configuration
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result = GeneralImageValidator.validate(dataset, config)
        
        # Process validation results
        if not result.is_valid:
            for error in result.errors:
                print(f"ERROR: {error}")
            for warning in result.warnings:
                print(f"WARNING: {warning}")
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 (required and specified) elements.
        
        Per DICOM PS3.3 C.7.6.1, no Type 1 elements are defined in General Image Module.
        All required elements are Type 2 (may be empty).
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing Type 1 elements
        """
        result = ValidationResult()
        # No Type 1 elements defined in General Image Module per DICOM PS3.3 C.7.6.1
        # All elements are Type 2, Type 2C, or Type 3
        return result
    
    @staticmethod
    def validate_type2_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2 (required but may be empty) elements.
        
        Per DICOM PS3.3 C.7.6.1, Instance Number is the only Type 2 element.
        Type 2 elements must be present but may contain an empty value.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing Type 2 elements
        """
        result = ValidationResult()
        
        # Instance Number (0020,0013) - Type 2
        if 'InstanceNumber' not in data:
            result.add_error(
                "Instance Number (0020,0013) is required (Type 2). "
                "This attribute identifies this image within its Series and must be present "
                "even if empty. Previously named 'Image Number' in earlier DICOM versions. "
                "To fix: Add data.InstanceNumber = '1' or appropriate sequence number. "
                "See DICOM PS3.3 C.7.6.1 for requirements."
            )
        else:
            # Validate Instance Number format (VR = IS - Integer String)
            try:
                instance_number = str(data.InstanceNumber)
                if instance_number and not instance_number.isdigit():
                    result.add_warning(
                        f"Instance Number (0020,0013) should contain only digits for VR IS compliance, "
                        f"got '{instance_number}'. While pydicom may accept this, strict DICOM compliance "
                        f"requires integer string format. See DICOM PS3.5 for VR IS definition."
                    )
            except (ValueError, TypeError):
                # If pydicom throws an error accessing the value, it means there's an invalid IS value
                # Try to access the raw value from the DataElement
                try:
                    if hasattr(data, 'data_element'):
                        data_element = data[0x0020, 0x0013] if hasattr(data, '__getitem__') else None
                        if data_element and hasattr(data_element, 'value'):
                            raw_value = str(data_element.value)
                            if raw_value and not raw_value.isdigit():
                                result.add_warning(
                                    f"Instance Number (0020,0013) should contain only digits for VR IS compliance, "
                                    f"got '{raw_value}'. While pydicom may accept this, strict DICOM compliance "
                                    f"requires integer string format. See DICOM PS3.5 for VR IS definition."
                                )
                except Exception:
                    # If we can't access the value at all, just note the issue
                    result.add_warning(
                        "Instance Number (0020,0013) contains a value that cannot be validated "
                        "for VR IS compliance. Ensure the value contains only digits."
                    )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2C conditional requirements per DICOM PS3.3 C.7.6.1.
        
        Validates all conditional requirements including:
        - Patient Orientation (required if no spatial orientation from other modules)
        - Content Date/Time (required if part of temporally related series)
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Type 2C: Patient Orientation conditional requirement
        GeneralImageValidator._validate_patient_orientation_requirement(data, result)
        
        # Type 2C: Content Date and Content Time for temporally related series
        GeneralImageValidator._validate_temporal_requirements(data, result)
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated values against DICOM standard.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Quality Control Image
        if 'QualityControlImage' in data:
            valid_values = [e.value for e in QualityControlImage]
            if data.QualityControlImage not in valid_values:
                result.add_error(
                    f"Quality Control Image (0028,0300) has invalid value '{data.QualityControlImage}'. "
                    f"Valid values: {valid_values}. Indicates presence of quality control material. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Burned In Annotation
        if 'BurnedInAnnotation' in data:
            valid_values = [e.value for e in BurnedInAnnotation]
            if data.BurnedInAnnotation not in valid_values:
                result.add_error(
                    f"Burned In Annotation (0028,0301) has invalid value '{data.BurnedInAnnotation}'. "
                    f"Valid values: {valid_values}. Indicates sufficient annotation to identify patient. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Recognizable Visual Features
        if 'RecognizableVisualFeatures' in data:
            valid_values = [e.value for e in RecognizableVisualFeatures]
            if data.RecognizableVisualFeatures not in valid_values:
                result.add_error(
                    f"Recognizable Visual Features (0028,0302) has invalid value '{data.RecognizableVisualFeatures}'. "
                    f"Valid values: {valid_values}. Indicates if image contains recognizable visual features "
                    "that could identify the patient. See DICOM PS3.3 C.7.6.1."
                )
        
        # Lossy Image Compression
        if 'LossyImageCompression' in data:
            valid_values = [e.value for e in LossyImageCompression]
            if data.LossyImageCompression not in valid_values:
                result.add_error(
                    f"Lossy Image Compression (0028,2110) has invalid value '{data.LossyImageCompression}'. "
                    f"Valid values: {valid_values}. Once set to '01', shall not be reset. "
                    "See DICOM PS3.3 C.*******.5."
                )
        
        # Presentation LUT Shape
        if 'PresentationLUTShape' in data:
            valid_values = [e.value for e in PresentationLUTShape]
            if data.PresentationLUTShape not in valid_values:
                result.add_error(
                    f"Presentation LUT Shape (2050,0020) has invalid value '{data.PresentationLUTShape}'. "
                    f"Valid values: {valid_values}. Specifies identity transformation for Presentation LUT. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Image Laterality
        if 'ImageLaterality' in data:
            valid_values = [e.value for e in ImageLaterality]
            if data.ImageLaterality not in valid_values:
                result.add_error(
                    f"Image Laterality (0020,0062) has invalid value '{data.ImageLaterality}'. "
                    f"Valid values: {valid_values}. Laterality of (possibly paired) body part examined. "
                    "Must be consistent with Primary Anatomic Structure Modifier Sequence and Laterality (0020,0060). "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Lossy Image Compression Method
        if 'LossyImageCompressionMethod' in data:
            valid_values = [e.value for e in LossyImageCompressionMethod]
            methods = data.LossyImageCompressionMethod
            if isinstance(methods, str):
                methods = [methods]
            for method in methods:
                if method not in valid_values:
                    result.add_error(
                        f"Lossy Image Compression Method (0028,2114) has invalid value '{method}'. "
                        f"Valid values: {valid_values}. Label for lossy compression method(s) applied. "
                        "Order should correspond to Lossy Image Compression Ratio values. "
                        "See DICOM PS3.3 C.*******.5.1."
                    )
        
        # Image Type validation
        if 'ImageType' in data:
            GeneralImageValidator._validate_image_type_structure(data, result)
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Validates Icon Image Sequence structure and required attributes
        according to DICOM PS3.3 C.*******.6.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # Icon Image Sequence validation
        if 'IconImageSequence' in data:
            if len(data.IconImageSequence) > 1:
                result.add_error(
                    "Icon Image Sequence (0088,0200) may contain only a single Item. "
                    "Icon image is representative of the Image. See DICOM PS3.3 C.*******.6."
                )
            
            if len(data.IconImageSequence) == 1:
                icon_item = data.IconImageSequence[0]
                
                # Required attributes for icon image
                required_attrs = ['Rows', 'Columns', 'SamplesPerPixel', 'PhotometricInterpretation',
                                'BitsAllocated', 'BitsStored', 'HighBit', 'PixelRepresentation']
                
                for attr in required_attrs:
                    if attr not in icon_item:
                        result.add_error(
                            f"Icon Image Sequence item missing required attribute {attr}. "
                            "Icon images must include Image Pixel Macro attributes. "
                            "See DICOM PS3.3 C.*******.6."
                        )
                
                # Validate icon image constraints per DICOM PS3.3 C.*******.6
                if 'SamplesPerPixel' in icon_item and icon_item.SamplesPerPixel != 1:
                    if 'PhotometricInterpretation' in icon_item:
                        if icon_item.PhotometricInterpretation not in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]:
                            result.add_error(
                                "Icon Image must use monochrome or palette color photometric interpretation. "
                                "Only MONOCHROME1, MONOCHROME2, or PALETTE COLOR are supported. "
                                "True color icon images are not supported. See DICOM PS3.3 C.*******.6."
                            )
                
                if 'BitsAllocated' in icon_item:
                    if icon_item.BitsAllocated not in [1, 8]:
                        result.add_error(
                            f"Icon Image Bits Allocated must be 1 or 8, got {icon_item.BitsAllocated}. "
                            "See DICOM PS3.3 C.*******.6."
                        )
        
        # Real World Value Mapping Sequence validation
        GeneralImageValidator._validate_real_world_value_mapping_sequence(data, result)
        
        return result
    
    @staticmethod
    def validate_cross_field_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate cross-field consistency and relationships.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for inconsistent field relationships
        """
        result = ValidationResult()
        
        # Validate lossy compression attribute consistency
        GeneralImageValidator._validate_lossy_compression_consistency(data, result)
        
        # Validate Image Type consistency
        GeneralImageValidator._validate_image_type_consistency(data, result)
        
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations for General Image Module.
        
        Performs comprehensive validation of all DICOM PS3.3 C.7.6.1 requirements
        with intelligent conditional logic and detailed error reporting using zero-copy optimization.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Comprehensive validation result with structured error and warning lists
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(GeneralImageValidator.validate_required_elements(data))
        result.merge(GeneralImageValidator.validate_type2_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(GeneralImageValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(GeneralImageValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(GeneralImageValidator.validate_sequence_structures(data))
        
        if config.validate_conditional_requirements:
            result.merge(GeneralImageValidator.validate_cross_field_consistency(data))
        
        return result

    @staticmethod
    def _validate_patient_orientation_requirement(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Patient Orientation Type 2C conditional requirement.
        
        Per DICOM PS3.3 C.7.6.1: Patient Orientation is required if image does not require
        Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032)
        or if image does not require Image Orientation (Slide) (0048,0102).
        """
        has_patient_orientation = 'PatientOrientation' in data
        has_image_orientation_patient = 'ImageOrientationPatient' in data
        has_image_position_patient = 'ImagePositionPatient' in data
        has_image_orientation_slide = 'ImageOrientationSlide' in data
        
        # Check if image has spatial orientation information from other modules
        has_spatial_orientation = (
            (has_image_orientation_patient and has_image_position_patient) or
            has_image_orientation_slide
        )
        
        # If no spatial orientation from other modules, Patient Orientation should be present
        if not has_spatial_orientation and not has_patient_orientation:
            result.add_error(
                "Patient Orientation (0020,0020) is required (Type 2C) when image does not have "
                "Image Orientation (Patient) (0020,0037) with Image Position (Patient) (0020,0032) "
                "or Image Orientation (Slide) (0048,0102). "
                "Patient Orientation specifies anatomical direction of rows and columns. "
                "See DICOM PS3.3 C.*******.1 for format requirements."
            )
        
        # Validate Patient Orientation format when present
        if has_patient_orientation:
            GeneralImageValidator._validate_patient_orientation_format(data, result)

    @staticmethod
    def _validate_patient_orientation_format(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Patient Orientation format requirements."""
        orientation = data.PatientOrientation
        
        # Should be 2 values when not empty
        if orientation != "" and orientation != []:
            if isinstance(orientation, str) and orientation != "":
                # Single string should not be used for non-empty orientation
                result.add_warning(
                    "Patient Orientation (0020,0020) should contain two values designating "
                    "row and column directions when not empty. See DICOM PS3.3 C.*******.1."
                )
            elif isinstance(orientation, (list, MultiValue)) and len(orientation) != 2:
                result.add_error(
                    f"Patient Orientation (0020,0020) must contain exactly 2 values, got {len(orientation)}. "
                    "See DICOM PS3.3 C.*******.1."
                )
    
    @staticmethod
    def _validate_temporal_requirements(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate temporal element requirements for temporally related series.
        
        Per DICOM PS3.3 C.7.6.1: Content Date and Content Time are Type 2C -
        required if image is part of a Series in which the images are temporally related.
        """
        has_content_date = 'ContentDate' in data
        has_content_time = 'ContentTime' in data
        
        if has_content_date and not has_content_time:
            result.add_error(
                "Content Time (0008,0033) is required (Type 2C) when Content Date (0008,0023) is present "
                "for temporally related series. Both date and time should be provided together. "
                "See DICOM PS3.3 C.7.6.1."
            )
        elif has_content_time and not has_content_date:
            result.add_error(
                "Content Date (0008,0023) is required (Type 2C) when Content Time (0008,0033) is present "
                "for temporally related series. Both date and time should be provided together. "
                "See DICOM PS3.3 C.7.6.1."
            )
    
    @staticmethod
    def _validate_image_type_structure(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Image Type multi-value structure per DICOM PS3.3 C.*******.2."""
        image_type = data.ImageType

        # Check if it's a multi-value field (list or MultiValue)
        if isinstance(image_type, str):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.*******.2."
            )
            return

        # Accept both list and MultiValue types
        if not isinstance(image_type, (list, MultiValue)):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.*******.2."
            )
            return

        if len(image_type) < 2:
            result.add_error(
                f"Image Type (0008,0008) must contain at least 2 values, got {len(image_type)}. "
                "Value 1: Pixel Data Characteristics, Value 2: Patient Examination Characteristics. "
                "See DICOM PS3.3 C.*******.2."
            )
            return

        # Value 1 - Pixel Data Characteristics
        if image_type[0] not in ["ORIGINAL", "DERIVED"]:
            result.add_error(
                f"Image Type (0008,0008) Value 1 (Pixel Data Characteristics) has invalid value '{image_type[0]}'. "
                "Valid values: ORIGINAL (original/source data), DERIVED (derived from other images). "
                "See DICOM PS3.3 C.*******.2."
            )

        # Value 2 - Patient Examination Characteristics
        if image_type[1] not in ["PRIMARY", "SECONDARY"]:
            result.add_error(
                f"Image Type (0008,0008) Value 2 (Patient Examination Characteristics) has invalid value '{image_type[1]}'. "
                "Valid values: PRIMARY (direct result of examination), SECONDARY (created after examination). "
                "See DICOM PS3.3 C.*******.2."
            )
    
    
    
    
    @staticmethod
    def _validate_lossy_compression_consistency(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate lossy compression attribute consistency."""
        
        has_lossy_compression = 'LossyImageCompression' in data
        has_compression_ratio = 'LossyImageCompressionRatio' in data
        has_compression_method = 'LossyImageCompressionMethod' in data
        
        # If lossy compression is "01", ratio and method should be present
        if has_lossy_compression and data.LossyImageCompression == "01":
            if not has_compression_ratio:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'. This provides important "
                    "information about the degree of compression applied. "
                    "To fix: Add data.LossyImageCompressionRatio = [2.5] or appropriate ratio value(s)."
                )
            if not has_compression_method:
                result.add_warning(
                    "Lossy Image Compression Method (0028,2114) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'. This identifies the specific "
                    "compression algorithm used. "
                    "To fix: Add data.LossyImageCompressionMethod = ['ISO_10918_1'] or appropriate method."
                )
        
        # If ratio and method are both present, they should have corresponding values
        if has_compression_ratio and has_compression_method:
            ratio_data = data.LossyImageCompressionRatio
            method_data = data.LossyImageCompressionMethod
            
            # Handle both list/MultiValue and single values
            ratio_count = len(ratio_data) if hasattr(ratio_data, '__len__') and not isinstance(ratio_data, str) else 1
            method_count = len(method_data) if hasattr(method_data, '__len__') and not isinstance(method_data, str) else 1
            
            if ratio_count != method_count:
                result.add_warning(
                    f"Lossy Image Compression Ratio (0028,2112) and Method (0028,2114) "
                    f"should have corresponding number of values. Found {ratio_count} ratio(s) "
                    f"and {method_count} method(s). The order of values should correspond "
                    f"to successive lossy compression steps applied to the image. "
                    f"See DICOM PS3.3 C.*******.5.2."
                )

    @staticmethod
    def _validate_real_world_value_mapping_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Real World Value Mapping Sequence structure."""
        if 'RealWorldValueMappingSequence' not in data:
            return
        
        sequence = data.RealWorldValueMappingSequence
        
        for i, item in enumerate(sequence):
            if 'RealWorldValueMappingSequence' not in item and \
               'RealWorldValueFirstValueMapped' not in item and \
               'RealWorldValueLastValueMapped' not in item and \
               'RealWorldValueLUTData' not in item:
                result.add_warning(
                    f"Real World Value Mapping Sequence item {i+1} appears to be empty. "
                    f"Empty mapping items provide no value conversion information. "
                    f"Consider including mapping attributes such as RealWorldValueFirstValueMapped, "
                    f"RealWorldValueLastValueMapped, or RealWorldValueLUTData for proper "
                    f"real world value conversion. See DICOM PS3.3 C.7.6.16-12b for macro definition."
                )

    @staticmethod
    def _validate_image_type_consistency(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Image Type consistency and derived image requirements.
        
        Per DICOM PS3.3 C.*******.2, derived images should have different SOP Instance UID
        if pixel data differences affect professional interpretation.
        """
        if 'ImageType' not in data:
            return
            
        image_type = data.ImageType
        # Handle both list/tuple and pydicom MultiValue objects
        if isinstance(image_type, (list, tuple, MultiValue)) and len(image_type) >= 1:
            if image_type[0] == "DERIVED":
                # For derived images, provide guidance about SOP Instance UID requirements
                result.add_warning(
                    "Image Type indicates DERIVED image. Per DICOM PS3.3 C.*******.2, "
                    "derived images should have a SOP Instance UID different from all source images "
                    "if pixel data differences are expected to affect professional interpretation. "
                    "This validation cannot check SOP Instance UID uniqueness without source image context. "
                    "Ensure derived images have unique SOP Instance UIDs when pixel data changes affect interpretation."
                )
