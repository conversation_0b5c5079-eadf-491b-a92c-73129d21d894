"""General Series Module DICOM validation - PS3.3 C.7.3.1

This module implements comprehensive validation for the DICOM General Series Module
according to DICOM PS3.3 Section C.7.3.1, ensuring complete compliance with
Type 1, Type 2, Type 2C, Type 1C, and Type 3 requirements.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class GeneralSeriesValidator(BaseValidator):
    """Validator for DICOM General Series Module (PS3.3 C.7.3.1)."""
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 (required) elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Modality (0008,0060) - Type 1
        if 'Modality' not in data:
            result.add_error(
                "Modality (0008,0060) is required (Type 1). "
                "Specify the type of equipment that originally acquired or produced the data. "
                "Use standard DICOM modality values (e.g., 'CT', 'MR', 'RTDOSE'). See DICOM PS3.3 C.*******.1."
            )
        
        # Series Instance UID (0020,000E) - Type 1
        if 'SeriesInstanceUID' not in data:
            result.add_error(
                "Series Instance UID (0020,000E) is required (Type 1). "
                "Provide a unique identifier for this Series following DICOM UID construction rules. "
                "Format: root.suffix where root is your organization's registered UID. See DICOM PS3.5."
            )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Type 2C: Laterality required for paired body parts if Image/Frame/Measurement Laterality not present
        if 'BodyPartExamined' in data:
            body_part = data.BodyPartExamined
            paired_body_parts = {
                'BREAST', 'EYE', 'KIDNEY', 'LUNG', 'OVARY', 'TESTIS', 
                'ARM', 'LEG', 'HAND', 'FOOT', 'SHOULDER', 'ELBOW', 
                'WRIST', 'HIP', 'KNEE', 'ANKLE', 'EAR', 'ADRENAL',
                'UTERINE_TUBE', 'OVARIAN'
            }
            
            is_paired_structure = any(
                part.upper() in body_part.upper() or
                body_part.upper() in part
                for part in paired_body_parts
            )
            
            if is_paired_structure:
                has_image_laterality = 'ImageLaterality' in data
                has_frame_laterality = 'FrameLaterality' in data
                has_measurement_laterality = 'MeasurementLaterality' in data
                has_laterality = 'Laterality' in data
                
                if not (has_image_laterality or has_frame_laterality or has_measurement_laterality or has_laterality):
                    result.add_error(
                        f"Laterality (0020,0060) is required (Type 2C) for paired body part '{body_part}'. "
                        "DICOM PS3.3 C.7.3.1 requires laterality when examining paired structures "
                        "and Image Laterality, Frame Laterality, or Measurement Laterality are not present."
                    )
        
        # Type 2C: Patient Position required for specific SOP Classes
        if 'SOPClassUID' in data:
            sop_class_uid = data.SOPClassUID
            required_sop_classes = [
                "1.2.840.10008.5.1.4.1.1.2",    # CT Image Storage
                "1.2.840.10008.5.1.4.1.1.4",    # MR Image Storage
                "1.2.840.10008.5.1.4.1.1.2.1",  # Enhanced CT Image Storage
                "1.2.840.10008.5.1.4.1.1.4.1",  # Enhanced MR Image Storage
                "1.2.840.10008.5.1.4.1.1.4.3",  # Enhanced Color MR Image Storage
                "1.2.840.10008.5.1.4.1.1.4.2"   # MR Spectroscopy Storage
            ]
            
            if sop_class_uid in required_sop_classes:
                has_patient_orientation = 'PatientOrientationCodeSequence' in data
                has_patient_position = 'PatientPosition' in data
                
                if not has_patient_orientation and not has_patient_position:
                    result.add_error(
                        "Patient Position (0018,5100) is required (Type 2C) for this SOP Class "
                        "when Patient Orientation Code Sequence (0054,0410) is not present. "
                        f"SOP Class UID: {sop_class_uid}. See DICOM PS3.3 C.7.3.1."
                    )
        
        # Type 1C: Anatomical Orientation Type required for non-human organisms
        is_non_human = (
            'PatientSpeciesDescription' in data or 
            'PatientSpeciesCodeSequence' in data
        )
        
        if is_non_human and 'AnatomicalOrientationType' not in data:
            result.add_error(
                "Anatomical Orientation Type (0010,2210) is required (Type 1C) "
                "for non-human organisms with non-bipedal anatomical frame of reference. "
                "DICOM PS3.3 C.7.3.1 requires this attribute for proper anatomical orientation."
            )
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated values against DICOM specifications.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Modality (0008,0060) - extensive validation against DICOM defined terms
        if 'Modality' in data:
            modality = data.Modality
            if modality:
                valid_modalities = {
                    # Current defined terms from DICOM PS3.3 C.*******.1
                    'ANN', 'AR', 'ASMT', 'AU', 'BDUS', 'BI', 'BMD', 'CFM', 'CR', 'CT',
                    'CTPROTOCOL', 'DMS', 'DG', 'DOC', 'DX', 'ECG', 'EEG', 'EMG', 'EOG',
                    'EPS', 'ES', 'FID', 'GM', 'HC', 'HD', 'IO', 'IOL', 'IVOCT', 'IVUS',
                    'KER', 'KO', 'LEN', 'LS', 'MG', 'MR', 'M3D', 'NM', 'OAM', 'OCT',
                    'OP', 'OPM', 'OPT', 'OPTBSV', 'OPTENF', 'OPV', 'OSS', 'OT', 'PA',
                    'PLAN', 'POS', 'PR', 'PT', 'PX', 'REG', 'RESP', 'RF', 'RG', 'RTDOSE',
                    'RTIMAGE', 'RTINTENT', 'RTPLAN', 'RTRAD', 'RTRECORD', 'RTSEGANN',
                    'RTSTRUCT', 'RWV', 'SEG', 'SM', 'SMR', 'SR', 'SRF', 'STAIN',
                    'TEXTUREMAP', 'TG', 'US', 'VA', 'XA', 'XAPROTOCOL', 'XC'
                }
                
                if modality not in valid_modalities:
                    result.add_warning(
                        f"Modality (0008,0060) value '{modality}' is not a recognized DICOM defined term. "
                        "Refer to DICOM PS3.3 C.*******.1 for valid modality values. Common values include: "
                        "'CT', 'MR', 'RTDOSE', 'RTSTRUCT', 'RTPLAN', 'RTIMAGE', 'US', 'XA', 'CR', 'DX'. "
                        "Using non-standard values may cause interoperability issues with DICOM viewers and PACS systems."
                    )
        
        # Laterality (0020,0060)
        if 'Laterality' in data:
            laterality = data.Laterality
            if laterality and laterality not in ["R", "L"]:
                result.add_error(
                    f"Laterality (0020,0060) value '{laterality}' is invalid. "
                    "Valid values are 'R' (right) or 'L' (left). See DICOM PS3.3 C.7.3.1."
                )
        
        # Anatomical Orientation Type (0010,2210)
        if 'AnatomicalOrientationType' in data:
            orientation_type = data.AnatomicalOrientationType
            if orientation_type and orientation_type not in ["BIPED", "QUADRUPED"]:
                result.add_error(
                    f"Anatomical Orientation Type (0010,2210) value '{orientation_type}' is invalid. "
                    "Valid values are 'BIPED' or 'QUADRUPED'. See DICOM PS3.3 C.7.3.1."
                )
        
        # Patient Position (0018,5100)
        if 'PatientPosition' in data:
            patient_position = data.PatientPosition
            if patient_position:
                valid_positions = [
                    "HFP", "HFS", "HFDR", "HFDL", "HFV", "HFI",
                    "FFDR", "FFDL", "FFP", "FFS", "FFV", "FFI",
                    "LFP", "LFS", "LFDR", "LFDL",
                    "RFP", "RFS", "RFDR", "RFDL",
                    "AFP", "AFS", "AFDR", "AFDL",
                    "PFP", "PFS", "PFDR", "PFDL"
                ]
                if patient_position not in valid_positions:
                    result.add_error(
                        f"Patient Position (0018,5100) value '{patient_position}' is invalid. "
                        "See DICOM PS3.3 C.*******.2 for valid patient position values."
                    )
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # Performing Physician Identification Sequence validation
        if 'PerformingPhysicianIdentificationSequence' in data and 'PerformingPhysicianName' in data:
            performing_phys_seq = data.PerformingPhysicianIdentificationSequence
            performing_phys_name = data.PerformingPhysicianName
            if performing_phys_seq and performing_phys_name:
                performing_names = performing_phys_name.split('\\') if isinstance(performing_phys_name, str) else []
                if len(performing_phys_seq) != len(performing_names):
                    result.add_warning(
                        "Performing Physician Identification Sequence (0008,1052): "
                        "Number of items should correspond to Performing Physician's Name (0008,1050)"
                    )
        
        # Operator Identification Sequence validation
        if 'OperatorIdentificationSequence' in data and 'OperatorsName' in data:
            operator_seq = data.OperatorIdentificationSequence
            operator_name = data.OperatorsName
            if operator_seq and operator_name:
                operator_names = operator_name.split('\\') if isinstance(operator_name, str) else []
                if len(operator_seq) != len(operator_names):
                    result.add_warning(
                        "Operator Identification Sequence (0008,1072): "
                        "Number of items should correspond to Operators' Name (0008,1070)"
                    )
        
        # Referenced Performed Procedure Step Sequence validation
        if 'ReferencedPerformedProcedureStepSequence' in data:
            ref_pps_seq = data.ReferencedPerformedProcedureStepSequence
            if len(ref_pps_seq) > 1:
                result.add_error(
                    "Referenced Performed Procedure Step Sequence (0008,1111): "
                    "Only a single Item is permitted in this Sequence per DICOM PS3.3 C.7.3.1. "
                    f"Found {len(ref_pps_seq)} items. Remove excess items to comply with standard."
                )
        
        # Related Series Sequence validation
        if 'RelatedSeriesSequence' in data:
            related_series_seq = data.RelatedSeriesSequence
            for i, item in enumerate(related_series_seq):
                if 'StudyInstanceUID' not in item or not item.StudyInstanceUID:
                    result.add_error(
                        f"Related Series Sequence item {i}: "
                        "Study Instance UID (0020,000D) is required"
                    )
                if 'SeriesInstanceUID' not in item or not item.SeriesInstanceUID:
                    result.add_error(
                        f"Related Series Sequence item {i}: "
                        "Series Instance UID (0020,000E) is required"
                    )
        
        # Series Description Code Sequence validation
        if 'SeriesDescriptionCodeSequence' in data:
            series_desc_seq = data.SeriesDescriptionCodeSequence
            if len(series_desc_seq) > 1:
                result.add_error(
                    "Series Description Code Sequence (0008,103F): "
                    "Only a single Item is permitted in this Sequence. "
                    "Remove additional sequence items to comply with DICOM standard."
                )
        
        # Request Attributes Sequence validation (Type 3)
        if 'RequestAttributesSequence' in data:
            request_attr_seq = data.RequestAttributesSequence
            for i, item in enumerate(request_attr_seq):
                if 'ScheduledProcedureStepID' not in item or not item.ScheduledProcedureStepID:
                    result.add_error(
                        f"Request Attributes Sequence item {i}: "
                        "Scheduled Procedure Step ID (0040,0009) is required within sequence item. "
                        "See DICOM PS3.3 C.7.3.1 for Request Attributes Sequence requirements."
                    )
        
        return result
    
    @staticmethod
    def validate_cross_field_dependencies(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate cross-field dependencies and logical relationships.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid cross-field relationships
        """
        result = ValidationResult()
        
        # Validate Treatment Session UID (Type 3) if present
        if 'TreatmentSessionUID' in data:
            treatment_session_uid = data.TreatmentSessionUID
            if treatment_session_uid:
                # Use base validator for UID format validation
                BaseValidator.validate_uid_format(
                    uid=treatment_session_uid,
                    field_name='Treatment Session UID (300A,0700)',
                    result=result
                )
                
                # Additional context validation for RT modalities
                if 'Modality' in data:
                    modality = data.Modality
                    rt_modalities = ['RTDOSE', 'RTIMAGE', 'RTPLAN', 'RTSTRUCT', 'RTRECORD']
                    if modality not in rt_modalities:
                        result.add_warning(
                            f"Treatment Session UID (300A,0700) is typically used with RT modalities "
                            f"but current modality is '{modality}'. Verify this is appropriate for your use case."
                        )
        
        # Validate pixel value consistency
        smallest_pixel = None
        largest_pixel = None
        
        if 'SmallestPixelValueInSeries' in data:
            smallest_pixel = data.SmallestPixelValueInSeries
        if 'LargestPixelValueInSeries' in data:
            largest_pixel = data.LargestPixelValueInSeries
            
        if smallest_pixel is not None and largest_pixel is not None:
            try:
                if int(smallest_pixel) > int(largest_pixel):
                    result.add_error(
                        f"Smallest Pixel Value in Series ({smallest_pixel}) cannot be greater than "
                        f"Largest Pixel Value in Series ({largest_pixel})"
                    )
            except (ValueError, TypeError):
                result.add_warning(
                    "Pixel value elements should contain valid numeric values. "
                    "Ensure Smallest/Largest Pixel Value in Series contain integer values."
                )
        
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Series Module requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
            
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(GeneralSeriesValidator.validate_required_elements(data))
        
        # Validate Type 2 elements (required but may be empty)
        if 'SeriesNumber' not in data:
            result.add_warning(
                "Series Number (0020,0011) should be present (Type 2). "
                "While empty values are permitted for Type 2 elements, providing a series number "
                "helps identify and organize the Series within a Study. Use integer values (e.g., 1, 2, 3)."
            )
        
        if config.validate_conditional_requirements:
            result.merge(GeneralSeriesValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(GeneralSeriesValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(GeneralSeriesValidator.validate_sequence_structures(data))
        
        if config.validate_cross_field_dependencies:
            result.merge(GeneralSeriesValidator.validate_cross_field_dependencies(data))
        
        return result
