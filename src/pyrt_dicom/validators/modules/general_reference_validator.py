"""General Reference Module DICOM validation - PS3.3 C.12.4

Validates all requirements for the General Reference Module including:
- Type 1C conditional requirements (Patient Orientation when Spatial Locations Preserved is REORIENTED_ONLY)
- Type 1 requirements within sequences (Purpose of Reference Code Sequence in Referenced Instance Sequence)
- Enumerated value validation (Spatial Locations Preserved)
- Sequence structure validation (required SOP Class and Instance UIDs)
- Semantic validation (images not in Source Instance Sequence)
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.common_enums import SpatialLocationsPreserved

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class GeneralReferenceValidator(BaseValidator):
    """Validator for DICOM General Reference Module (PS3.3 C.12.4).

    Validates all requirements from DICOM PS3.3 C.12.4 including:
    - Type 1C conditional requirements (Patient Orientation when Spatial Locations Preserved is REORIENTED_ONLY)
    - Type 1 requirements within sequences (Purpose of Reference Code Sequence in Referenced Instance Sequence)
    - Enumerated value validation (Spatial Locations Preserved values)
    - Sequence structure validation (required SOP Class and Instance UIDs from macros)
    - Semantic validation (business logic constraints)

    This validator provides comprehensive error checking with clear, actionable
    error messages to guide users in creating DICOM-compliant datasets.
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Note: All elements in General Reference Module are Type 3 (optional),
        so this method returns an empty result but is included for consistency
        with the validation pattern.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no required elements in this module)
        """
        result = ValidationResult()
        # All elements in General Reference Module are Type 3 (optional)
        # No Type 1 or Type 2 elements to validate
        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Validates:
        - Patient Orientation (0020,0020) is Type 1C: Required if Spatial Locations Preserved is REORIENTED_ONLY

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Check Source Image Sequence for Type 1C requirements
        if 'SourceImageSequence' in data:
            source_seq = data.SourceImageSequence
            for i, item in enumerate(source_seq):
                # Type 1C: Patient Orientation required when Spatial Locations Preserved is REORIENTED_ONLY
                if 'SpatialLocationsPreserved' in item:
                    spatial_preserved = item.SpatialLocationsPreserved
                    if spatial_preserved == "REORIENTED_ONLY":
                        if 'PatientOrientation' not in item:
                            result.add_error(
                                f"Source Image Sequence item {i}: Patient Orientation (0020,0020) is Type 1C "
                                f"(required when Spatial Locations Preserved is REORIENTED_ONLY). "
                                f"See DICOM PS3.3 C.12.4."
                            )
                        else:
                            # Validate format if present
                            patient_orientation = item.PatientOrientation
                            # Handle both regular Python lists/tuples and pydicom MultiValue objects
                            try:
                                if len(patient_orientation) != 2:
                                    result.add_error(
                                        f"Source Image Sequence item {i}: Patient Orientation (0020,0020) "
                                        f"must be a list/array of exactly 2 values, got {len(patient_orientation)} values "
                                        f"with value {patient_orientation}. See DICOM PS3.3 C.12.4."
                                    )
                            except (TypeError, AttributeError):
                                result.add_error(
                                    f"Source Image Sequence item {i}: Patient Orientation (0020,0020) "
                                    f"must be a list/array of exactly 2 values, got {type(patient_orientation).__name__} "
                                    f"with value {patient_orientation}. See DICOM PS3.3 C.12.4."
                                )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Validates:
        - Spatial Locations Preserved (0028,135A): YES, NO, REORIENTED_ONLY

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Check Source Image Sequence for enumerated values
        if 'SourceImageSequence' in data:
            source_seq = data.SourceImageSequence
            for i, item in enumerate(source_seq):
                # Validate Spatial Locations Preserved enumerated values
                if 'SpatialLocationsPreserved' in item:
                    spatial_preserved = item.SpatialLocationsPreserved
                    valid_values = ["YES", "NO", "REORIENTED_ONLY"]
                    if spatial_preserved not in valid_values:
                        result.add_warning(
                            f"Source Image Sequence item {i}: Spatial Locations Preserved (0028,135A) "
                            f"value '{spatial_preserved}' should be one of: {', '.join(valid_values)}. "
                            f"See DICOM PS3.3 C.12.4."
                        )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Validates:
        - Image SOP Instance Reference Macro requirements (Table 10-3)
        - SOP Instance Reference Macro requirements (Table 10-11)
        - Type 1 requirements within sequences
        - Semantic constraints (images not in Source Instance Sequence)

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Validate Referenced Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            data, 'ReferencedImageSequence', result
        )

        # Validate Referenced Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        # Purpose of Reference Code Sequence is Type 1 (required) in this sequence
        if 'ReferencedInstanceSequence' in data:
            ref_instance_seq = data.ReferencedInstanceSequence
            for i, item in enumerate(ref_instance_seq):
                GeneralReferenceValidator._validate_sop_instance_reference_item(
                    item, f"Referenced Instance Sequence item {i}", result
                )

                # Type 1 requirement: Purpose of Reference Code Sequence
                if 'PurposeOfReferenceCodeSequence' not in item:
                    result.add_error(
                        f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                        f"is Type 1 (required). Uses CID 7004 for waveforms, CID 7022 for RT instances. "
                        f"See DICOM PS3.3 C.12.4."
                    )

        # Validate Source Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            data, 'SourceImageSequence', result
        )

        # Validate Source Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        if 'SourceInstanceSequence' in data:
            source_instance_seq = data.SourceInstanceSequence
            for i, item in enumerate(source_instance_seq):
                GeneralReferenceValidator._validate_sop_instance_reference_item(
                    item, f"Source Instance Sequence item {i}", result
                )

        # Validate semantic constraints
        GeneralReferenceValidator._validate_semantic_constraints(data, result)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements (though none exist in this module)
        result.merge(GeneralReferenceValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(GeneralReferenceValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(GeneralReferenceValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(GeneralReferenceValidator.validate_sequence_structures(data))

        return result

    @staticmethod
    def _validate_image_sop_instance_reference_sequence(
        data: Union[Dataset, 'BaseModule'], sequence_name: str, result: ValidationResult
    ) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.12.4.

        Validates requirements from included macros:
        - Table 10-3 Image SOP Instance Reference Macro (Referenced Image Sequence, Source Image Sequence)
        - Table 10-11 SOP Instance Reference Macro (Referenced Instance Sequence, Source Instance Sequence)
        - Type 1 requirements within sequences
        """

        # Referenced Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            dataset, 'ReferencedImageSequence', result
        )

        # Referenced Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        # Purpose of Reference Code Sequence is Type 1 (required) in this sequence
        ref_instance_seq = getattr(dataset, 'ReferencedInstanceSequence', [])
        for i, item in enumerate(ref_instance_seq):
            GeneralReferenceValidator._validate_sop_instance_reference_item(
                item, f"Referenced Instance Sequence item {i}", result
            )

            # Type 1 requirement: Purpose of Reference Code Sequence
            purpose_seq = getattr(item, 'PurposeOfReferenceCodeSequence', None)
            if not purpose_seq:
                result.add_error(
                    f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                    f"is Type 1 (required). Uses CID 7004 for waveforms, CID 7022 for RT instances. "
                    f"See DICOM PS3.3 C.12.4."
                )
            else:
                # Check if it's a sequence-like object with length
                try:
                    seq_length = len(purpose_seq)
                    if seq_length == 0:
                        result.add_error(
                            f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                            f"must contain at least one item when present."
                        )
                    elif seq_length > 1:
                        result.add_warning(
                            f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                            f"should contain only a single item per DICOM standard. Found {seq_length} items."
                        )
                except (TypeError, AttributeError):
                    # If it's not a sequence-like object, that's also an error
                    result.add_error(
                        f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                        f"must be a sequence containing at least one item."
                    )

        # Source Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            dataset, 'SourceImageSequence', result
        )

        # Source Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        source_instance_seq = getattr(dataset, 'SourceInstanceSequence', [])
        for i, item in enumerate(source_instance_seq):
            GeneralReferenceValidator._validate_sop_instance_reference_item(
                item, f"Source Instance Sequence item {i}", result
            )

    @staticmethod
    def _validate_image_sop_instance_reference_sequence(
        data: Union[Dataset, 'BaseModule'], sequence_name: str, result: ValidationResult
    ) -> None:
        """Validate Image SOP Instance Reference Macro requirements (Table 10-3).

        Args:
            data: Dataset OR BaseModule containing the sequence
            sequence_name: Name of the sequence attribute
            result: ValidationResult to update
        """
        if sequence_name in data:
            sequence = getattr(data, sequence_name)
            for i, item in enumerate(sequence):
                item_name = f"{sequence_name} item {i}"

                # Required elements from Table 10-3 Image SOP Instance Reference Macro
                if 'ReferencedSOPClassUID' not in item:
                    result.add_error(
                        f"{item_name}: Referenced SOP Class UID (0008,1150) is required "
                        f"from Table 10-3 Image SOP Instance Reference Macro. "
                        f"See DICOM PS3.3 C.12.4."
                    )
                if 'ReferencedSOPInstanceUID' not in item:
                    result.add_error(
                        f"{item_name}: Referenced SOP Instance UID (0008,1155) is required "
                        f"from Table 10-3 Image SOP Instance Reference Macro. "
                        f"See DICOM PS3.3 C.12.4."
                    )

    @staticmethod
    def _validate_sop_instance_reference_item(
        item: Dataset, item_name: str, result: ValidationResult
    ) -> None:
        """Validate SOP Instance Reference Macro requirements (Table 10-11).

        Args:
            item: Dataset item to validate
            item_name: Name/description of the item for error messages
            result: ValidationResult to update
        """
        # Required elements from Table 10-11 SOP Instance Reference Macro
        if 'ReferencedSOPClassUID' not in item:
            result.add_error(
                f"{item_name}: Referenced SOP Class UID (0008,1150) is required "
                f"from Table 10-11 SOP Instance Reference Macro. "
                f"See DICOM PS3.3 C.12.4."
            )
        if 'ReferencedSOPInstanceUID' not in item:
            result.add_error(
                f"{item_name}: Referenced SOP Instance UID (0008,1155) is required "
                f"from Table 10-11 SOP Instance Reference Macro. "
                f"See DICOM PS3.3 C.12.4."
            )

    @staticmethod
    def _validate_semantic_constraints(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate semantic constraints and business logic per DICOM PS3.3 C.12.4.

        Validates:
        - Images shall not be referenced by Source Instance Sequence (only non-image instances)
        - Purpose of Reference Code Sequence uses appropriate CID codes
        - Sequence usage consistency with derivation information
        """

        # Semantic constraint: Images shall not be referenced by Source Instance Sequence
        if 'SourceInstanceSequence' in data:
            source_instance_seq = data.SourceInstanceSequence
            for i, item in enumerate(source_instance_seq):
                if 'ReferencedSOPClassUID' in item:
                    sop_class_uid = item.ReferencedSOPClassUID
                    # Check if this is an image SOP class (basic check for common image SOP classes)
                    image_sop_classes = [
                        '1.2.840.10008.5.1.4.1.1.1',    # CR Image Storage
                        '1.2.840.10008.5.1.4.1.1.2',    # CT Image Storage
                        '1.2.840.10008.5.1.4.1.1.4',    # MR Image Storage
                        '1.2.840.10008.5.1.4.1.1.6.1',  # US Image Storage
                        '1.2.840.10008.5.1.4.1.1.12.1', # X-Ray Angiographic Image Storage
                        '1.2.840.10008.5.1.4.1.1.20',   # Nuclear Medicine Image Storage
                        '1.2.840.10008.5.1.4.1.1.77.1.4', # VL Photographic Image Storage
                    ]

                    if sop_class_uid in image_sop_classes:
                        result.add_error(
                            f"Source Instance Sequence item {i}: Images shall NOT be referenced by "
                            f"Source Instance Sequence (0042,0013). Use Source Image Sequence (0008,2112) "
                            f"for image references. Referenced SOP Class UID {sop_class_uid} appears to be "
                            f"an image SOP class. See DICOM PS3.3 C.12.4.1.2."
                        )

        # Validate derivation consistency
        GeneralReferenceValidator._validate_derivation_consistency(data, result)

    @staticmethod
    def _validate_derivation_consistency(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate consistency between derivation information and source sequences.

        Args:
            data: Dataset OR BaseModule to validate
            result: ValidationResult to update
        """
        has_derivation_desc = 'DerivationDescription' in data
        has_derivation_code = 'DerivationCodeSequence' in data
        has_source_images = 'SourceImageSequence' in data
        has_source_instances = 'SourceInstanceSequence' in data

        # If derivation information is present, it suggests this is a derived image
        if has_derivation_desc or has_derivation_code:
            if not (has_source_images or has_source_instances):
                result.add_warning(
                    "Derivation Description (0008,2111) or Derivation Code Sequence (0008,9215) "
                    "is present but no Source Image Sequence (0008,2112) or Source Instance Sequence "
                    "(0042,0013) is provided. Consider adding source information to document the "
                    "derivation process. See DICOM PS3.3 C.********."
                )

        # If source sequences are present, derivation information might be helpful
        if (has_source_images or has_source_instances) and not (has_derivation_desc or has_derivation_code):
            result.add_warning(
                "Source Image Sequence (0008,2112) or Source Instance Sequence (0042,0013) "
                "is present but no Derivation Description (0008,2111) or Derivation Code Sequence "
                "(0008,9215) is provided. Consider adding derivation information to document how "
                "the image was derived. See DICOM PS3.3 C.********."
            )
