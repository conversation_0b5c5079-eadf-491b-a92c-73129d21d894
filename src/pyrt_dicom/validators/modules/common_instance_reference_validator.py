"""Common Instance Reference Module DICOM validation - PS3.3 C.12.2

This validator ensures 100% compliance with DICOM PS3.3 C.12.2 Common Instance Reference Module
specifications, including proper validation of hierarchical reference structures, conditional
requirements, and embedded macro implementations.

Key Validation Areas:
- Type 1C conditional sequence requirements
- SOP Instance Reference Macro (Table 10-11) compliance
- Series and Instance Reference Macro (Table 10-4) compliance
- Hierarchical reference structure integrity
- Cross-reference consistency validation
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class CommonInstanceReferenceValidator(BaseValidator):
    """Validator for DICOM Common Instance Reference Module (PS3.3 C.12.2).

    Provides comprehensive validation of hierarchical SOP Instance references
    within DICOM datasets, ensuring full compliance with DICOM standard
    requirements for cross-study and same-study instance references.

    Validation Coverage:
    - Type 1C conditional requirements for reference sequences
    - SOP Instance Reference Macro (Table 10-11) validation
    - Series and Instance Reference Macro (Table 10-4) validation
    - Sequence structure and cardinality requirements
    - Reference hierarchy consistency checks
    """
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Common Instance Reference Module has no unconditional Type 1 elements
        # All elements are Type 1C conditional based on context
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Check for empty sequences (present but with no items)
        if 'ReferencedSeriesSequence' in data and len(getattr(data, 'ReferencedSeriesSequence', [])) == 0:
            result.add_warning(
                "Referenced Series Sequence (0008,1115) is present but empty. "
                "If no same-study instances are referenced, this sequence should be absent. "
                "If instances are referenced, at least one item must be included per DICOM PS3.3 C.12.2."
            )

        if 'StudiesContainingOtherReferencedInstancesSequence' in data and len(getattr(data, 'StudiesContainingOtherReferencedInstancesSequence', [])) == 0:
            result.add_warning(
                "Studies Containing Other Referenced Instances Sequence (0008,1200) is present but empty. "
                "If no cross-study instances are referenced, this sequence should be absent. "
                "If instances are referenced, at least one item must be included per DICOM PS3.3 C.12.2."
            )

        # Valid states: both present, one present, or neither present
        # No additional validation needed as the conditional logic is context-dependent
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints and UID formats.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Validate UID formats in sequence structures
        CommonInstanceReferenceValidator._validate_uid_formats(data, result)
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # Validate Referenced Series Sequence structure (same-study references)
        CommonInstanceReferenceValidator._validate_referenced_series_structure(data, result)
        
        # Validate Studies Containing Other Referenced Instances Sequence structure (cross-study references)
        CommonInstanceReferenceValidator._validate_other_studies_structure(data, result)
        
        # Validate SOP Instance Reference Macro (Table 10-11) requirements
        CommonInstanceReferenceValidator._validate_sop_instance_reference_macro(data, result)
        
        # Validate reference hierarchy consistency
        CommonInstanceReferenceValidator._validate_reference_hierarchy(data, result)
        
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(CommonInstanceReferenceValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(CommonInstanceReferenceValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(CommonInstanceReferenceValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(CommonInstanceReferenceValidator.validate_sequence_structures(data))
        
        return result
    
    @staticmethod
    def _validate_uid_formats(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate UID formats in all sequence items.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any issues found
        """
        def validate_uid_format(uid: str, uid_name: str, location: str) -> None:
            """Validate a single UID format."""
            if not CommonInstanceReferenceValidator._is_valid_uid(uid):
                result.add_error(
                    f"{location}: {uid_name} '{uid}' "
                    f"is not a valid DICOM UID format. UIDs must follow ISO 8824 standard with numeric components "
                    f"separated by periods. Solution: Use a properly formatted DICOM UID."
                )

        # Validate Referenced Series Sequence UIDs
        if 'ReferencedSeriesSequence' in data:
            ref_series_seq = data.ReferencedSeriesSequence
            for i, series_item in enumerate(ref_series_seq):
                if 'SeriesInstanceUID' in series_item:
                    validate_uid_format(
                        series_item.SeriesInstanceUID,
                        "Series Instance UID (0020,000E)",
                        f"Referenced Series Sequence item {i}"
                    )
                
                # Check instance UIDs within series
                if 'ReferencedInstanceSequence' in series_item:
                    ref_instance_seq = series_item.ReferencedInstanceSequence
                    for j, instance_item in enumerate(ref_instance_seq):
                        location = f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}"
                        
                        if 'ReferencedSOPClassUID' in instance_item:
                            validate_uid_format(
                                instance_item.ReferencedSOPClassUID,
                                "Referenced SOP Class UID (0008,1150)",
                                location
                            )
                        
                        if 'ReferencedSOPInstanceUID' in instance_item:
                            validate_uid_format(
                                instance_item.ReferencedSOPInstanceUID,
                                "Referenced SOP Instance UID (0008,1155)",
                                location
                            )

        # Validate Studies Containing Other Referenced Instances Sequence UIDs
        if 'StudiesContainingOtherReferencedInstancesSequence' in data:
            other_studies_seq = data.StudiesContainingOtherReferencedInstancesSequence
            for i, study_item in enumerate(other_studies_seq):
                if 'StudyInstanceUID' in study_item:
                    validate_uid_format(
                        study_item.StudyInstanceUID,
                        "Study Instance UID (0020,000D)",
                        f"Studies Containing Other Referenced Instances Sequence item {i}"
                    )
                
                # Check nested series and instance UIDs
                if 'ReferencedSeriesSequence' in study_item:
                    ref_series_seq = study_item.ReferencedSeriesSequence
                    for j, series_item in enumerate(ref_series_seq):
                        if 'SeriesInstanceUID' in series_item:
                            validate_uid_format(
                                series_item.SeriesInstanceUID,
                                "Series Instance UID (0020,000E)",
                                f"Studies Containing Other Referenced Instances Sequence item {i}, Referenced Series Sequence item {j}"
                            )
                        
                        if 'ReferencedInstanceSequence' in series_item:
                            ref_instance_seq = series_item.ReferencedInstanceSequence
                            for k, instance_item in enumerate(ref_instance_seq):
                                location = (f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                          f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}")
                                
                                if 'ReferencedSOPClassUID' in instance_item:
                                    validate_uid_format(
                                        instance_item.ReferencedSOPClassUID,
                                        "Referenced SOP Class UID (0008,1150)",
                                        location
                                    )
                                
                                if 'ReferencedSOPInstanceUID' in instance_item:
                                    validate_uid_format(
                                        instance_item.ReferencedSOPInstanceUID,
                                        "Referenced SOP Instance UID (0008,1155)",
                                        location
                                    )

    @staticmethod
    def _validate_referenced_series_structure(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Referenced Series Sequence structure (same-study references).
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any issues found
        """
        if 'ReferencedSeriesSequence' not in data:
            return
            
        ref_series_seq = data.ReferencedSeriesSequence
        for i, series_item in enumerate(ref_series_seq):
            # Series Instance UID (0020,000E) is Type 1 in Referenced Series Sequence
            if 'SeriesInstanceUID' not in series_item:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Series Instance UID (0020,000E) is required (Type 1). "
                    f"Each series reference must include a unique series identifier to establish proper "
                    f"hierarchical relationships per DICOM PS3.3 C.12.2. "
                    f"Solution: Set SeriesInstanceUID to a valid DICOM UID."
                )

            # Referenced Instance Sequence (0008,114A) is Type 1 within Referenced Series Sequence
            if 'ReferencedInstanceSequence' not in series_item:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is required (Type 1). "
                    f"Each series reference must contain at least one instance reference to be meaningful "
                    f"per DICOM PS3.3 C.12.2. Solution: Add at least one referenced instance item."
                )
            elif len(series_item.ReferencedInstanceSequence) == 0:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is empty. "
                    f"DICOM PS3.3 C.12.2 requires 'One or more Items shall be included in this Sequence'. "
                    f"Solution: Add at least one referenced instance item or remove the empty series reference."
                )

    @staticmethod
    def _validate_other_studies_structure(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Studies Containing Other Referenced Instances Sequence structure (cross-study references).
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any issues found
        """
        if 'StudiesContainingOtherReferencedInstancesSequence' not in data:
            return
            
        other_studies_seq = data.StudiesContainingOtherReferencedInstancesSequence
        for i, study_item in enumerate(other_studies_seq):
            # Study Instance UID (0020,000D) is Type 1 in Studies Containing Other Referenced Instances Sequence
            if 'StudyInstanceUID' not in study_item:
                result.add_error(
                    f"Studies Containing Other Referenced Instances Sequence item {i}: "
                    f"Study Instance UID (0020,000D) is required (Type 1). "
                    f"Each cross-study reference must include a unique study identifier to establish proper "
                    f"hierarchical relationships per DICOM PS3.3 C.12.2. "
                    f"Solution: Set StudyInstanceUID to a valid DICOM UID."
                )

            # Validate Series and Instance Reference Macro (Table 10-4) implementation
            CommonInstanceReferenceValidator._validate_series_instance_reference_macro(study_item, i, result)
    
    @staticmethod
    def _validate_sop_instance_reference_macro(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate SOP Instance Reference Macro (Table 10-11) requirements within sequences.

        Validates all referenced instance items according to DICOM PS3.3 Table 10-11
        SOP Instance Reference Macro specifications.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any issues found
        """

        def validate_referenced_instance_item(item: Dataset, location: str) -> None:
            """Validate a single referenced instance item against Table 10-11 requirements."""
            # Referenced SOP Class UID (0008,1150) - Type 1
            if 'ReferencedSOPClassUID' not in item:
                result.add_error(
                    f"{location}: Referenced SOP Class UID (0008,1150) is required (Type 1). "
                    f"This attribute uniquely identifies the referenced SOP Class per DICOM PS3.3 Table 10-11. "
                    f"Solution: Set ReferencedSOPClassUID to a valid DICOM SOP Class UID."
                )

            # Referenced SOP Instance UID (0008,1155) - Type 1
            if 'ReferencedSOPInstanceUID' not in item:
                result.add_error(
                    f"{location}: Referenced SOP Instance UID (0008,1155) is required (Type 1). "
                    f"This attribute uniquely identifies the referenced SOP Instance per DICOM PS3.3 Table 10-11. "
                    f"Solution: Set ReferencedSOPInstanceUID to a valid DICOM SOP Instance UID."
                )

            # Referenced Frame Number (0008,1160) - Type 1C validation
            # Note: Full validation requires external context about whether the referenced SOP Instance is multi-frame
            if 'ReferencedFrameNumber' in item:
                frame_numbers = item.ReferencedFrameNumber
                if not isinstance(frame_numbers, (list, tuple)) or len(frame_numbers) == 0:
                    result.add_warning(
                        f"{location}: Referenced Frame Number (0008,1160) is present but empty or invalid. "
                        f"If specified, it must contain at least one frame number per DICOM PS3.3 Table 10-11."
                    )

            # Referenced Segment Number (0062,000B) - Type 1C validation
            # Note: Full validation requires external context about whether the referenced SOP Instance is a Segmentation
            if 'ReferencedSegmentNumber' in item:
                segment_numbers = item.ReferencedSegmentNumber
                if not isinstance(segment_numbers, (list, tuple)) or len(segment_numbers) == 0:
                    result.add_warning(
                        f"{location}: Referenced Segment Number (0062,000B) is present but empty or invalid. "
                        f"If specified, it must contain at least one segment number per DICOM PS3.3 Table 10-11."
                    )

        # Validate Referenced Series Sequence items (same-study references)
        if 'ReferencedSeriesSequence' in data:
            ref_series_seq = data.ReferencedSeriesSequence
            for i, series_item in enumerate(ref_series_seq):
                if 'ReferencedInstanceSequence' in series_item:
                    ref_instance_seq = series_item.ReferencedInstanceSequence
                    for j, instance_item in enumerate(ref_instance_seq):
                        location = f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}"
                        validate_referenced_instance_item(instance_item, location)

        # Validate Studies Containing Other Referenced Instances Sequence items (cross-study references)
        if 'StudiesContainingOtherReferencedInstancesSequence' in data:
            other_studies_seq = data.StudiesContainingOtherReferencedInstancesSequence
            for i, study_item in enumerate(other_studies_seq):
                if 'ReferencedSeriesSequence' in study_item:
                    ref_series_seq = study_item.ReferencedSeriesSequence
                    for j, series_item in enumerate(ref_series_seq):
                        if 'ReferencedInstanceSequence' in series_item:
                            ref_instance_seq = series_item.ReferencedInstanceSequence
                            for k, instance_item in enumerate(ref_instance_seq):
                                location = (f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                           f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}")
                                validate_referenced_instance_item(instance_item, location)

    @staticmethod
    def _validate_series_instance_reference_macro(study_item: Dataset, study_index: int, result: ValidationResult) -> None:
        """Validate Series and Instance Reference Macro (Table 10-4) within study items.

        Args:
            study_item: Study item from Studies Containing Other Referenced Instances Sequence
            study_index: Index of the study item for error reporting
            result: ValidationResult to update with any issues found
        """
        if 'ReferencedSeriesSequence' in study_item:
            ref_series_seq = study_item.ReferencedSeriesSequence
            if len(ref_series_seq) == 0:
                result.add_warning(
                    f"Studies Containing Other Referenced Instances Sequence item {study_index}: "
                    f"Referenced Series Sequence is present but empty. If no series are referenced, "
                    f"this sequence should be absent per DICOM PS3.3 Table 10-4."
                )

            for j, series_item in enumerate(ref_series_seq):
                # Series Instance UID (0020,000E) - Type 1 in Table 10-4
                if 'SeriesInstanceUID' not in series_item:
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Series Instance UID (0020,000E) is required (Type 1). "
                        f"Each series reference must include a unique series identifier per DICOM PS3.3 Table 10-4. "
                        f"Solution: Set SeriesInstanceUID to a valid DICOM UID."
                    )

                # Referenced Instance Sequence (0008,114A) - Type 1 in Table 10-4
                if 'ReferencedInstanceSequence' not in series_item:
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Referenced Instance Sequence (0008,114A) is required (Type 1). "
                        f"Each series reference must contain at least one instance reference per DICOM PS3.3 Table 10-4. "
                        f"Solution: Add at least one referenced instance item."
                    )
                elif len(series_item.ReferencedInstanceSequence) == 0:
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Referenced Instance Sequence (0008,114A) is empty. "
                        f"DICOM PS3.3 Table 10-4 requires 'One or more Items shall be included in this Sequence'. "
                        f"Solution: Add at least one referenced instance item."
                    )

    @staticmethod
    def _validate_reference_hierarchy(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate reference hierarchy consistency and completeness.

        Performs additional validation to ensure the hierarchical reference structure
        is logically consistent and complete according to DICOM requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update with any issues found
        """
        # Count total references for consistency checking
        total_same_study_instances = 0
        total_cross_study_instances = 0

        # Count same-study references
        if 'ReferencedSeriesSequence' in data:
            ref_series_seq = data.ReferencedSeriesSequence
            for series_item in ref_series_seq:
                if 'ReferencedInstanceSequence' in series_item:
                    ref_instance_seq = series_item.ReferencedInstanceSequence
                    total_same_study_instances += len(ref_instance_seq)

        # Count cross-study references
        if 'StudiesContainingOtherReferencedInstancesSequence' in data:
            other_studies_seq = data.StudiesContainingOtherReferencedInstancesSequence
            for study_item in other_studies_seq:
                if 'ReferencedSeriesSequence' in study_item:
                    ref_series_seq = study_item.ReferencedSeriesSequence
                    for series_item in ref_series_seq:
                        if 'ReferencedInstanceSequence' in series_item:
                            ref_instance_seq = series_item.ReferencedInstanceSequence
                            total_cross_study_instances += len(ref_instance_seq)

        # Provide informational summary
        total_references = total_same_study_instances + total_cross_study_instances
        if total_references == 0:
            result.add_warning(
                "Common Instance Reference Module contains no instance references. "
                "If this module is present, it typically indicates that instance references should be included. "
                "Consider whether this module is necessary if no references are being made."
            )

    @staticmethod
    def _is_valid_uid(uid: str) -> bool:
        """Validate DICOM UID format according to ISO 8824 standard.

        Args:
            uid: UID string to validate

        Returns:
            bool: True if UID format is valid, False otherwise
        """
        if not uid or not isinstance(uid, str):
            return False

        # DICOM UIDs must be composed of numeric components separated by periods
        # Maximum length is 64 characters
        if len(uid) > 64:
            return False

        # Must contain only digits and periods
        if not all(c.isdigit() or c == '.' for c in uid):
            return False

        # Must not start or end with a period
        if uid.startswith('.') or uid.endswith('.'):
            return False

        # Must not contain consecutive periods
        if '..' in uid:
            return False

        # Each component must be numeric and not start with 0 (except for single digit 0)
        components = uid.split('.')
        for component in components:
            if not component:  # Empty component
                return False
            if len(component) > 1 and component.startswith('0'):  # Leading zeros not allowed
                return False
            if not component.isdigit():  # Must be numeric
                return False

        return True
