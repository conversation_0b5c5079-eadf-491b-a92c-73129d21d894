"""Frame of Reference Module DICOM validation - PS3.3 C.7.4.1

This validator ensures compliance with DICOM PS3.3 C.7.4.1 Frame of Reference Module
requirements, including Type 1 and Type 2 element validation, UID format validation,
and position reference indicator semantic validation.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class FrameOfReferenceValidator(BaseValidator):
    """Validator for DICOM Frame of Reference Module (PS3.3 C.7.4.1).

    Independent validator that works on pydicom Dataset OR BaseModule instances.
    Validates Frame of Reference Module requirements including:
    - Type 1: Frame of Reference UID (0020,0052)
    - Type 2: Position Reference Indicator (0020,1040)
    - UID format compliance per DICOM PS3.5
    - Position reference indicator semantic validation
    - Context-specific warnings for special coordinate systems
    """
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Frame of Reference UID (0020,0052) Type 1 - Required and must have value
        if 'FrameOfReferenceUID' not in data:
            result.add_error(
                "Frame of Reference UID (0020,0052) is required (Type 1). "
                "This attribute uniquely identifies the Frame of Reference for a Series."
            )
        elif 'FrameOfReferenceUID' in data and not data.FrameOfReferenceUID:
            result.add_error(
                "Frame of Reference UID (0020,0052) must be a non-empty string. "
                "Provide a valid DICOM UID that uniquely identifies the Frame of Reference."
            )

        # Position Reference Indicator (0020,1040) Type 2 - Required but can be empty
        if 'PositionReferenceIndicator' not in data:
            result.add_error(
                "Position Reference Indicator (0020,1040) is required (Type 2). "
                "This attribute specifies the part of the imaging target used as a reference point. "
                "It may be empty for cases like mammographic images where no meaningful reference exists."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        The Frame of Reference Module has no Type 1C or Type 2C elements per DICOM PS3.3 C.7.4.1.
        This method is provided for API consistency with other validators.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no conditional requirements)
        """
        return ValidationResult()

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Validate Position Reference Indicator values and context
        if 'PositionReferenceIndicator' in data:
            position_ref = data.PositionReferenceIndicator
            if position_ref:  # Only validate if not empty (Type 2 allows empty)
                FrameOfReferenceValidator._validate_position_reference_semantic(position_ref, result)

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        The Frame of Reference Module has no sequence elements per DICOM PS3.3 C.7.4.1.
        This method is provided for API consistency with other validators.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no sequence requirements)
        """
        return ValidationResult()

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(FrameOfReferenceValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(FrameOfReferenceValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(FrameOfReferenceValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(FrameOfReferenceValidator.validate_sequence_structures(data))

        # Validate UID format compliance
        FrameOfReferenceValidator._validate_uid_format(data, result)

        return result
    
    @staticmethod
    def _validate_uid_format(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate UID format compliance."""
        if 'FrameOfReferenceUID' in data:
            uid_value = data.FrameOfReferenceUID
            if uid_value and not FrameOfReferenceValidator._is_valid_uid_format(uid_value):
                result.add_warning(
                    f"Frame of Reference UID (0020,0052) '{uid_value}' may not comply with DICOM UID format. "
                    "UIDs should follow the format specified in DICOM PS3.5 Section 9."
                )
    
    @staticmethod
    def _validate_position_reference_semantic(position_ref: str, result: ValidationResult) -> None:
        """Validate position reference indicator semantic values and provide context-specific guidance."""
        # Known valid position reference indicators
        valid_patient_references = [
            "ILIAC_CREST", "ORBITAL_MEDIAL", "STERNAL_NOTCH", "SYMPHYSIS_PUBIS",
            "XIPHOID", "LOWER_COSTAL_MARGIN", "EXTERNAL_AUDITORY_MEATUS"
        ]
        valid_slide_references = ["SLIDE_CORNER"]
        valid_corneal_references = ["CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L"]

        all_valid_references = valid_patient_references + valid_slide_references + valid_corneal_references

        # Check if it's a known standard value
        if position_ref not in all_valid_references:
            result.add_warning(
                f"Position Reference Indicator '{position_ref}' is not a standard DICOM value. "
                f"Standard values include: {', '.join(all_valid_references)}. "
                "Custom values are allowed but should be clearly documented."
            )

        # Provide context-specific guidance
        if position_ref in valid_corneal_references:
            result.add_warning(
                f"Corneal coordinate system detected ({position_ref}). "
                "Ensure this Frame of Reference is used with appropriate ophthalmic imaging modalities."
            )
        elif position_ref == "SLIDE_CORNER":
            result.add_warning(
                "Slide-based coordinate system detected. "
                "Ensure this Frame of Reference is used with microscopy or pathology imaging."
            )
    
    @staticmethod
    def _is_valid_uid_format(uid: str) -> bool:
        """Check if UID follows basic DICOM format requirements."""
        if not uid or not isinstance(uid, str):
            return False

        # Basic UID format checks
        # UIDs should contain only digits and dots, start and end with digits
        import re
        uid_pattern = r'^[0-9]+(\.[0-9]+)*$'
        if not re.match(uid_pattern, uid):
            return False

        # Check length (UIDs should not exceed 64 characters)
        if len(uid) > 64:
            return False

        # Check for leading zeros in components (not allowed)
        components = uid.split('.')
        for component in components:
            if len(component) > 1 and component.startswith('0'):
                return False

        return True
