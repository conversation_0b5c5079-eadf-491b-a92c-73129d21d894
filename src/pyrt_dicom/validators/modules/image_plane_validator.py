"""Image Plane Module DICOM validation - PS3.3 C.7.6.2

Comprehensive validation of the Image Plane Module requirements including:
- Type 1/2/3 element validation
- Paired requirement for Image Position and Image Orientation
- Geometric constraints (orthogonality, normalization, right-handedness)
- Patient-Based Coordinate System validation
- Cross-field relationships and spatial consistency
- Granular validation methods for targeted validation scenarios
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from pydicom.multival import MultiValue
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ImagePlaneValidator(BaseValidator):
    """Validator for DICOM Image Plane Module (PS3.3 C.7.6.2).
    
    Implements comprehensive validation of spatial image plane attributes
    including geometric constraints required by the DICOM standard.
    
    Key Validation Features:
    - Paired requirement validation for spatial elements
    - Geometric constraint validation (orthogonality, normalization)
    - Right-handed coordinate system validation
    - Cross-field relationship validation
    - Type-specific element validation (Type 1/2/3)
    - Granular validation methods for integration with modules
    """
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 (required and must be specified) attributes.
        
        Per DICOM PS3.3 C.7.6.2, Type 1 attributes must be present and contain valid data.
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Pixel Spacing (0028,0030) Type 1
        if 'PixelSpacing' not in data:
            result.add_error(
                "Pixel Spacing (0028,0030) is required (Type 1). "
                "This attribute specifies the physical distance in mm between pixel centers. "
                "Format: [row_spacing, column_spacing]. "
                "See DICOM PS3.3 C.7.6.2 Table C.7-10."
            )
        else:
            if not isinstance(data.PixelSpacing, (list, tuple, MultiValue)) or len(data.PixelSpacing) != 2:
                result.add_error(
                    "Pixel Spacing (0028,0030) must be a pair of values [row_spacing, column_spacing]. "
                    "Got: {0}. Expected format: [float, float] representing spacing in mm. "
                    "See DICOM PS3.3 Section 10.7.1.3 for detailed explanation.".format(
                        type(data.PixelSpacing).__name__ if 'PixelSpacing' in data else 'None'
                    )
                )
            else:
                try:
                    row_spacing, col_spacing = float(data.PixelSpacing[0]), float(data.PixelSpacing[1])
                    if row_spacing <= 0 or col_spacing <= 0:
                        result.add_error(
                            f"Pixel Spacing (0028,0030) values must be positive. "
                            f"Got: [{row_spacing}, {col_spacing}]. "
                            f"Physical distances cannot be zero or negative."
                        )
                except (ValueError, TypeError):
                    result.add_error(
                        f"Pixel Spacing (0028,0030) values must be numeric. "
                        f"Got: {data.PixelSpacing}. "
                        f"Expected: [float, float] representing spacing in mm."
                    )
        
        # Image Orientation (Patient) (0020,0037) Type 1
        if 'ImageOrientationPatient' not in data:
            result.add_error(
                "Image Orientation (Patient) (0020,0037) is required (Type 1). "
                "This attribute specifies direction cosines of the first row and column with respect to the patient. "
                "Format: [row_x, row_y, row_z, col_x, col_y, col_z]. "
                "See DICOM PS3.3 C.*******.1 for detailed explanation."
            )
        else:
            if not isinstance(data.ImageOrientationPatient, (list, tuple, MultiValue)) or len(data.ImageOrientationPatient) != 6:
                result.add_error(
                    "Image Orientation (Patient) (0020,0037) must contain exactly 6 direction cosine values. "
                    "Format: [row_x, row_y, row_z, col_x, col_y, col_z]. "
                    f"Got {len(data.ImageOrientationPatient) if isinstance(data.ImageOrientationPatient, (list, tuple, MultiValue)) else 0} values. "
                    "These represent the direction cosines of the first row and first column with respect to the patient coordinate system."
                )
            else:
                try:
                    _ = [float(x) for x in data.ImageOrientationPatient]
                except (ValueError, TypeError):
                    result.add_error(
                        f"Image Orientation (Patient) (0020,0037) values must be numeric. "
                        f"Got: {data.ImageOrientationPatient}. "
                        f"Expected: [float, float, float, float, float, float] representing direction cosines."
                    )
        
        # Image Position (Patient) (0020,0032) Type 1
        if 'ImagePositionPatient' not in data:
            result.add_error(
                "Image Position (Patient) (0020,0032) is required (Type 1). "
                "This attribute specifies the x, y, and z coordinates of the upper left hand corner of the image in mm. "
                "Format: [x, y, z]. "
                "See DICOM PS3.3 C.*******.1 for detailed explanation."
            )
        else:
            if not isinstance(data.ImagePositionPatient, (list, tuple, MultiValue)) or len(data.ImagePositionPatient) != 3:
                result.add_error(
                    "Image Position (Patient) (0020,0032) must contain exactly 3 coordinate values [x, y, z]. "
                    f"Got {len(data.ImagePositionPatient) if isinstance(data.ImagePositionPatient, (list, tuple, MultiValue)) else 0} values. "
                    "These represent the coordinates of the center of the first voxel transmitted, in mm."
                )
            else:
                try:
                    _ = [float(x) for x in data.ImagePositionPatient]
                except (ValueError, TypeError):
                    result.add_error(
                        f"Image Position (Patient) (0020,0032) values must be numeric. "
                        f"Got: {data.ImagePositionPatient}. "
                        f"Expected: [float, float, float] representing coordinates in mm."
                    )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        For Image Plane Module, conditional requirements include:
        - Slice Thickness (0018,0050) Type 2 - required but can be empty
        - Paired requirement for Image Position and Image Orientation
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Slice Thickness (0018,0050) Type 2
        if 'SliceThickness' not in data:
            result.add_error(
                "Slice Thickness (0018,0050) is required (Type 2). "
                "This attribute may be empty but must be present. "
                "It specifies the nominal slice thickness in mm. "
                "See DICOM PS3.3 C.7.6.2 Table C.7-10."
            )
        
        # Validate paired requirements per DICOM PS3.3 C.*******.1
        ImagePlaneValidator._validate_paired_requirements(data, result)
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Image Plane Module has limited enumerated values, but validates numeric constraints.
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # No specific enumerated values for Image Plane Module
        # But validate spacing constraints - this should be a warning, not error
        if 'SpacingBetweenSlices' in data:
            try:
                spacing = float(data.SpacingBetweenSlices)
                if spacing < 0:
                    result.add_warning(
                        f"Spacing Between Slices (0018,0088) should not be negative "
                        f"unless specialized IOD defines the meaning of the sign. "
                        f"Got: {spacing} mm. "
                        f"Per DICOM PS3.3 C.7.6.2: 'If present, shall not be negative, unless specialized to define the meaning of the sign in a specialized IOD'."
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Spacing Between Slices (0018,0088) must be numeric when present. "
                    f"Got: {data.SpacingBetweenSlices}. "
                    f"Expected: numeric value representing spacing in mm."
                )
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Image Plane Module has no sequences, but validates related structures.
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # Image Plane Module has no sequence elements
        # This method is provided for consistency with the refactoring pattern
        
        return result
    
    @staticmethod
    def validate_geometric_constraints(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate geometric constraints for direction cosines per DICOM PS3.3 C.7.6.2.
        
        Per DICOM standard:
        - 'The row and column direction cosine vectors shall be orthogonal'
        - 'The row and column direction cosine vectors shall be normal' 
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for geometric constraint violations
        """
        result = ValidationResult()
        
        if 'ImageOrientationPatient' not in data:
            return result
        
        try:
            cosines = [float(x) for x in data.ImageOrientationPatient]
            if len(cosines) != 6:
                return result
            
            row_cosines = cosines[:3]
            col_cosines = cosines[3:]
            
            # Check orthogonality: dot product should be zero
            dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
            if abs(dot_product) > 1e-6:
                result.add_error(
                    f"Image Orientation (Patient) (0020,0037) row and column direction cosines "
                    f"must be orthogonal per DICOM PS3.3 C.7.6.2. "
                    f"Dot product = {dot_product:.6f}, should be ≈ 0. "
                    f"Row cosines: {row_cosines}, Column cosines: {col_cosines}. "
                    f"Ensure vectors are perpendicular to each other."
                )
            
            # Check normalization: magnitude should be 1.0
            row_magnitude = sum(r * r for r in row_cosines) ** 0.5
            col_magnitude = sum(c * c for c in col_cosines) ** 0.5
            
            if abs(row_magnitude - 1.0) > 1e-6:
                result.add_error(
                    f"Image Orientation (Patient) (0020,0037) row direction cosines "
                    f"must be normalized (unit vector) per DICOM PS3.3 C.7.6.2. "
                    f"Magnitude = {row_magnitude:.6f}, should be 1.0. "
                    f"Row cosines: {row_cosines}. "
                    f"Normalize the vector so its magnitude equals 1.0."
                )
            
            if abs(col_magnitude - 1.0) > 1e-6:
                result.add_error(
                    f"Image Orientation (Patient) (0020,0037) column direction cosines "
                    f"must be normalized (unit vector) per DICOM PS3.3 C.7.6.2. "
                    f"Magnitude = {col_magnitude:.6f}, should be 1.0. "
                    f"Column cosines: {col_cosines}. "
                    f"Normalize the vector so its magnitude equals 1.0."
                )
        
        except (ValueError, TypeError):
            # Numeric validation already handled in validate_required_elements
            pass
        
        return result
    
    @staticmethod
    def validate_coordinate_system(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Patient-Based Coordinate System requirements per DICOM PS3.3 C.7.6.2.
        
        'The Patient-Based Coordinate System is a right handed system, i.e., the vector 
        cross product of a unit vector along the positive x-axis and a unit vector along 
        the positive y-axis is equal to a unit vector along the positive z-axis.'
        
        Args:
            data: Dataset or BaseModule instance for zero-copy validation
            
        Returns:
            ValidationResult: Validation result with errors for coordinate system violations
        """
        result = ValidationResult()
        
        if 'ImageOrientationPatient' not in data:
            return result
        
        try:
            cosines = [float(x) for x in data.ImageOrientationPatient]
            if len(cosines) != 6:
                return result
            
            row_cosines = cosines[:3]
            col_cosines = cosines[3:]
            
            # Calculate cross product: row × column
            cross_product = [
                row_cosines[1] * col_cosines[2] - row_cosines[2] * col_cosines[1],
                row_cosines[2] * col_cosines[0] - row_cosines[0] * col_cosines[2],
                row_cosines[0] * col_cosines[1] - row_cosines[1] * col_cosines[0]
            ]
            
            # Cross product magnitude should be approximately 1.0 for orthogonal unit vectors
            magnitude = sum(x * x for x in cross_product) ** 0.5
            
            # Check if coordinate system is right-handed
            # For right-handed system, cross product should point in positive z direction
            if abs(magnitude - 1.0) > 1e-6:
                result.add_warning(
                    f"Image Orientation (Patient) (0020,0037) vectors may not form a proper "
                    f"orthonormal basis. Cross product magnitude = {magnitude:.6f}, should be ≈ 1.0. "
                    f"This may indicate the vectors are not perfectly orthogonal and normalized."
                )
            elif cross_product[2] <= 0:
                result.add_error(
                    f"Image Orientation (Patient) (0020,0037) does not define a right-handed "
                    f"coordinate system per DICOM PS3.3 C.7.6.2. "
                    f"Cross product z-component = {cross_product[2]:.6f}, should be > 0. "
                    f"Row cosines: {row_cosines}, Column cosines: {col_cosines}. "
                    f"The Patient-Based Coordinate System must be right-handed."
                )
        
        except (ValueError, TypeError):
            # Numeric validation already handled in validate_required_elements
            pass
        
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Image Plane Module requirements on any pydicom Dataset or BaseModule.
        
        Args:
            data: pydicom Dataset or BaseModule instance for zero-copy validation
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements (Type 1)
        result.merge(ImagePlaneValidator.validate_required_elements(data))
        
        # Always validate conditional requirements (Type 1C/2C)
        result.merge(ImagePlaneValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(ImagePlaneValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(ImagePlaneValidator.validate_sequence_structures(data))
        
        # Always validate geometric constraints (DICOM requirements)
        result.merge(ImagePlaneValidator.validate_geometric_constraints(data))
        
        # Always validate coordinate system requirements
        result.merge(ImagePlaneValidator.validate_coordinate_system(data))
        
        # Validate value ranges and cross-field relationships
        ImagePlaneValidator._validate_value_constraints(data, result)
        
        # Validate factory method input parameters (if applicable)
        ImagePlaneValidator._validate_input_parameters(data, result)
        
        return result
    
    @staticmethod
    def _validate_paired_requirements(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate paired requirements per DICOM PS3.3 C.*******.1.
        
        'Image Position (Patient) (0020,0032) and Image Orientation (Patient) (0020,0037) 
        shall be provide as a pair.'
        """
        
        has_position = 'ImagePositionPatient' in data
        has_orientation = 'ImageOrientationPatient' in data
        
        if has_position and not has_orientation:
            result.add_error(
                "Image Position (Patient) (0020,0032) and Image Orientation (Patient) (0020,0037) "
                "shall be provided as a pair per DICOM PS3.3 C.*******.1. "
                "Image Position is present but Image Orientation is missing. "
                "Both attributes are required to define the spatial relationship of the image plane."
            )
        elif has_orientation and not has_position:
            result.add_error(
                "Image Position (Patient) (0020,0032) and Image Orientation (Patient) (0020,0037) "
                "shall be provided as a pair per DICOM PS3.3 C.*******.1. "
                "Image Orientation is present but Image Position is missing. "
                "Both attributes are required to define the spatial relationship of the image plane."
            )
        # If neither is present, this is caught by Type 1 validation
        # If both are present, this requirement is satisfied
    
    @staticmethod
    def _validate_value_constraints(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
    
        """Validate value constraints, ranges, and cross-field relationships."""
        
        # Slice Thickness validation (Type 2 - can be empty but if present must be valid)
        if 'SliceThickness' in data:
            # Type 2 can be empty string - only validate if not empty
            if data.SliceThickness != "":
                try:
                    thickness = float(data.SliceThickness)
                    if thickness <= 0:
                        result.add_warning(
                            f"Slice Thickness (0018,0050) should be positive. "
                            f"Got: {thickness} mm. "
                            f"Negative or zero thickness may indicate invalid spatial configuration."
                        )
                except (ValueError, TypeError):
                    result.add_error(
                        f"Slice Thickness (0018,0050) must be numeric when present. "
                        f"Got: {data.SliceThickness}. "
                        f"Expected: numeric value representing thickness in mm or empty string."
                    )
        
        # Slice Location validation (Type 3 - optional)
        if 'SliceLocation' in data:
            try:
                _ = float(data.SliceLocation)
                # No specific constraints on slice location value - it's relative to implementation-specific reference
            except (ValueError, TypeError):
                result.add_error(
                    f"Slice Location (0020,1041) must be numeric when present. "
                    f"Got: {data.SliceLocation}. "
                    f"Expected: numeric value representing relative position in mm."
                )
        
        # Cross-validation: Spacing Between Slices vs Slice Thickness
        ImagePlaneValidator._validate_slice_spacing_relationships(data, result)
    
    @staticmethod
    def _validate_slice_spacing_relationships(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate relationships between slice spacing attributes."""
        
        if not ('SpacingBetweenSlices' in data and 'SliceThickness' in data):
            return
        
        # Skip validation if SliceThickness is empty (Type 2 can be empty)
        if data.SliceThickness == "":
            return
        
        try:
            spacing = float(data.SpacingBetweenSlices)
            thickness = float(data.SliceThickness)
            
            if spacing > 0 and thickness > 0:
                if spacing < thickness:
                    result.add_warning(
                        f"Spacing Between Slices ({spacing} mm) is less than Slice Thickness ({thickness} mm). "
                        f"This may indicate overlapping slices, which could affect dose calculation accuracy. "
                        f"Consider reviewing slice positioning or thickness values."
                    )
                elif spacing > thickness * 2:
                    result.add_warning(
                        f"Spacing Between Slices ({spacing} mm) is much larger than Slice Thickness ({thickness} mm). "
                        f"This may indicate significant gaps between slices, which could affect spatial coverage. "
                        f"Gap size: {spacing - thickness} mm. Consider reviewing slice spacing configuration."
                    )
                elif abs(spacing - thickness) < 1e-6:
                    # Perfect match - this is good for contiguous slices
                    pass
                else:
                    # Small difference - this may be intentional for slight overlap or gap
                    gap_or_overlap = spacing - thickness
                    if gap_or_overlap > 0:
                        result.add_warning(
                            f"Small gap between slices: {gap_or_overlap:.3f} mm. "
                            f"Spacing Between Slices ({spacing} mm) > Slice Thickness ({thickness} mm)."
                        )
        except (ValueError, TypeError):
            # Numeric validation already handled in individual attribute validation
            pass
    
    @staticmethod
    def _validate_input_parameters(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate input parameters that would be used in factory methods.
        
        This method provides validation for parameters that might be passed to
        from_required_elements() or with_optional_elements() methods.
        """
        # Validate pixel spacing format and values
        ImagePlaneValidator._validate_pixel_spacing_input(data, result)
        
        # Validate image orientation and position pair
        ImagePlaneValidator._validate_orientation_position_pair_input(data, result)
        
        # Validate spacing between slices input
        ImagePlaneValidator._validate_spacing_between_slices_input(data, result)
    
    @staticmethod
    def _validate_pixel_spacing_input(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate pixel spacing input parameter constraints."""
        if 'PixelSpacing' not in data:
            return
        
        pixel_spacing = data.PixelSpacing
        
        # Check format
        if not isinstance(pixel_spacing, (list, tuple, MultiValue)) or len(pixel_spacing) != 2:
            result.add_error(
                "Pixel Spacing (0028,0030) must be a list or tuple of 2 values: [row_spacing, column_spacing]. "
                "This constraint applies to factory method parameters."
            )
            return
        
        # Check values are positive
        try:
            row_spacing, col_spacing = float(pixel_spacing[0]), float(pixel_spacing[1])
            if row_spacing <= 0 or col_spacing <= 0:
                result.add_error(
                    f"Pixel Spacing (0028,0030) values must be positive: got [{row_spacing}, {col_spacing}]. "
                    f"Physical distances cannot be zero or negative."
                )
        except (ValueError, TypeError):
            result.add_error(
                f"Pixel Spacing (0028,0030) values must be numeric. "
                f"Got: {pixel_spacing}. Expected: [float, float] representing spacing in mm."
            )
    
    @staticmethod
    def _validate_orientation_position_pair_input(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate image orientation and position pair input constraints.
        
        Per DICOM PS3.3 C.*******.1: "Image Position (Patient) (0020,0032) and
        Image Orientation (Patient) (0020,0037) shall be provided as a pair."
        """
        has_orientation = 'ImageOrientationPatient' in data
        has_position = 'ImagePositionPatient' in data
        
        if not (has_orientation and has_position):
            return  # This is handled by Type 1 validation
        
        orientation = data.ImageOrientationPatient
        position = data.ImagePositionPatient
        
        # Validate Image Orientation (Patient) format
        if not isinstance(orientation, (list, tuple, MultiValue)) or len(orientation) != 6:
            result.add_error(
                "Image Orientation (Patient) (0020,0037) must be a list or tuple of 6 direction cosine values: "
                "[row_x, row_y, row_z, col_x, col_y, col_z]. This constraint applies to factory method parameters."
            )
        
        # Validate Image Position (Patient) format
        if not isinstance(position, (list, tuple, MultiValue)) or len(position) != 3:
            result.add_error(
                "Image Position (Patient) (0020,0032) must be a list or tuple of 3 coordinate values: [x, y, z]. "
                "This constraint applies to factory method parameters."
            )
        
        # Validate geometric constraints if formats are correct
        if (isinstance(orientation, (list, tuple, MultiValue)) and len(orientation) == 6 and 
            isinstance(position, (list, tuple, MultiValue)) and len(position) == 3):
            
            try:
                # Convert to float and validate geometric constraints
                float_orientation = [float(x) for x in orientation]
                [float(x) for x in position]  # Validates numeric conversion
                
                row_cosines = float_orientation[:3]
                col_cosines = float_orientation[3:]
                
                # Check orthogonality: dot product should be zero
                dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
                if abs(dot_product) > 1e-6:
                    result.add_error(
                        f"Image Orientation (Patient) (0020,0037) row and column direction cosines must be orthogonal. "
                        f"Dot product is {dot_product:.6f}, should be ≈ 0. "
                        f"Check that row and column vectors are perpendicular. "
                        f"This constraint applies to factory method parameters."
                    )
                
                # Check normalization: magnitude should be 1.0
                row_magnitude = sum(r * r for r in row_cosines) ** 0.5
                col_magnitude = sum(c * c for c in col_cosines) ** 0.5
                
                if abs(row_magnitude - 1.0) > 1e-6:
                    result.add_error(
                        f"Image Orientation (Patient) (0020,0037) row direction cosines must be normalized (unit vector). "
                        f"Magnitude is {row_magnitude:.6f}, should be 1.0. "
                        f"Row cosines: {row_cosines}. This constraint applies to factory method parameters."
                    )
                
                if abs(col_magnitude - 1.0) > 1e-6:
                    result.add_error(
                        f"Image Orientation (Patient) (0020,0037) column direction cosines must be normalized (unit vector). "
                        f"Magnitude is {col_magnitude:.6f}, should be 1.0. "
                        f"Column cosines: {col_cosines}. This constraint applies to factory method parameters."
                    )
            
            except (ValueError, TypeError):
                result.add_error(
                    "Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032) "
                    "values must be numeric. This constraint applies to factory method parameters."
                )
    
    @staticmethod
    def _validate_spacing_between_slices_input(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate spacing between slices input constraints."""
        if 'SpacingBetweenSlices' not in data:
            return
        
        try:
            spacing = float(data.SpacingBetweenSlices)
            if spacing < 0:
                result.add_error(
                    f"Spacing Between Slices (0018,0088) must not be negative unless "
                    f"specialized IOD defines the meaning of the sign (see DICOM PS3.3 C.7.6.2). "
                    f"Got: {spacing} mm. This constraint applies to factory method parameters."
                )
        except (ValueError, TypeError):
            result.add_error(
                f"Spacing Between Slices (0018,0088) must be numeric when present. "
                f"Got: {data.SpacingBetweenSlices}. "
                f"Expected: numeric value representing spacing in mm. "
                f"This constraint applies to factory method parameters."
            )
