"""Image Pixel Module DICOM validation - PS3.3 C.7.6.3

Validates the Image Pixel Module, Image Pixel Macro, and Image Pixel Description Macro
according to DICOM PS3.3 C.7.6.3. Implements comprehensive validation of all Type 1,
Type 1C, Type 2C conditional requirements, enumerated values, and complex pixel data
consistency rules.

Key Validation Areas:
- Type 1 required elements validation
- Complex Type 1C conditional requirements (pixel data, planar configuration, palette color, etc.)
- Photometric interpretation constraints on samples per pixel
- Bit allocation and representation consistency
- YBR format specific constraints (422, 420, ICT, RCT)
- Palette color lookup table consistency
- Extended offset table conditional requirements
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import PhotometricInterpretation, PlanarConfiguration, PixelRepresentation

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ImagePixelValidator(BaseValidator):
    """Validator for DICOM Image Pixel Module (PS3.3 C.7.6.3).

    Provides comprehensive validation of the Image Pixel Module including:
    - All Type 1 required elements from Image Pixel Description Macro
    - Complex Type 1C conditional requirements with detailed error messages
    - Photometric interpretation constraints and consistency validation
    - Bit allocation and representation validation with cross-field checks
    - YBR format specific validation (422, 420, ICT, RCT constraints)
    - Palette color lookup table validation and consistency checks
    - Extended offset table conditional validation
    - Pixel data consistency and format validation
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 (required) elements for Image Pixel Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        required_attrs = [
            ('SamplesPerPixel', '0028,0002', 'Number of samples (planes) in this image'),
            ('PhotometricInterpretation', '0028,0004', 'Intended interpretation of pixel data'),
            ('Rows', '0028,0010', 'Number of rows in the image'),
            ('Columns', '0028,0011', 'Number of columns in the image'),
            ('BitsAllocated', '0028,0100', 'Number of bits allocated for each pixel sample'),
            ('BitsStored', '0028,0101', 'Number of bits stored for each pixel sample'),
            ('HighBit', '0028,0102', 'Most significant bit for pixel sample data'),
            ('PixelRepresentation', '0028,0103', 'Data representation of pixel samples')
        ]

        for attr_name, tag, description in required_attrs:
            if attr_name not in data:
                result.add_error(
                    f"Missing required attribute {attr_name} ({tag}): {description}. "
                    f"This is a Type 1 element according to DICOM PS3.3 C.******* "
                    f"Image Pixel Description Macro."
                )

        # Validate specific Type 1 value constraints with detailed error messages
        ImagePixelValidator._validate_samples_per_pixel_values(data, result)
        ImagePixelValidator._validate_image_dimensions_values(data, result)
        ImagePixelValidator._validate_bit_allocation_values(data, result)
        ImagePixelValidator._validate_bit_relationships_values(data, result)

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements for Image Pixel Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Validate pixel data requirements (Type 1C - exactly one required)
        ImagePixelValidator._validate_pixel_data_requirements_values(data, result)

        # Validate planar configuration requirements (Type 1C)
        ImagePixelValidator._validate_planar_configuration_requirements_values(data, result)

        # Validate palette color requirements (Type 1C)
        ImagePixelValidator._validate_palette_color_requirements_values(data, result)

        # Validate pixel aspect ratio requirements (Type 1C)
        ImagePixelValidator._validate_pixel_aspect_ratio_requirements_values(data, result)

        # Validate extended offset table requirements (Type 1C)
        ImagePixelValidator._validate_extended_offset_table_requirements_values(data, result)

        # Validate pixel padding range limit requirements (Type 1C)
        ImagePixelValidator._validate_pixel_padding_requirements_values(data, result)

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints for Image Pixel Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Photometric Interpretation - validate against defined terms
        if 'PhotometricInterpretation' in data:
            valid_photometric_values = [e.value for e in PhotometricInterpretation]
            if data.PhotometricInterpretation not in valid_photometric_values:
                result.add_error(
                    f"Invalid Photometric Interpretation (0028,0004): '{data.PhotometricInterpretation}'. "
                    f"Valid values according to DICOM PS3.3 C.*******.2 are: {', '.join(valid_photometric_values)}. "
                    f"This attribute specifies the intended interpretation of the pixel data."
                )

        # Planar Configuration - validate enumerated values
        if 'PlanarConfiguration' in data:
            valid_planar_values = [str(e.value) for e in PlanarConfiguration]
            planar_str = str(data.PlanarConfiguration)
            if planar_str not in valid_planar_values:
                result.add_error(
                    f"Invalid Planar Configuration (0028,0006): '{data.PlanarConfiguration}'. "
                    f"Valid values are: 0 (color-by-pixel) or 1 (color-by-plane). "
                    f"According to DICOM PS3.3 C.*******.3, this indicates whether color pixel "
                    f"data are encoded color-by-pixel or color-by-plane."
                )

        # Pixel Representation - validate enumerated values
        if 'PixelRepresentation' in data:
            valid_pixel_rep_values = [str(e.value) for e in PixelRepresentation]
            pixel_rep_str = str(data.PixelRepresentation)
            if pixel_rep_str not in valid_pixel_rep_values:
                result.add_error(
                    f"Invalid Pixel Representation (0028,0103): '{data.PixelRepresentation}'. "
                    f"Valid values are: 0 (unsigned integer) or 1 (2's complement signed integer). "
                    f"According to DICOM PS3.3 C.*******, this specifies the data representation "
                    f"of the pixel samples."
                )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements for Image Pixel Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Validate palette color descriptor consistency
        ImagePixelValidator._validate_palette_descriptor_consistency_values(data, result)

        # Validate ICC Profile and Color Space constraints
        ImagePixelValidator._validate_color_profile_constraints_values(data, result)

        # Validate photometric interpretation specific constraints
        ImagePixelValidator._validate_photometric_constraints_values(data, result)

        # Validate YBR format specific requirements
        ImagePixelValidator._validate_ybr_format_constraints_values(data, result)

        # Validate palette color conditional logic (when palette attributes present with wrong photometric)
        ImagePixelValidator._validate_palette_color_conditional_logic_values(data, result)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations for Image Pixel Module.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(ImagePixelValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(ImagePixelValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(ImagePixelValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(ImagePixelValidator.validate_sequence_structures(data))

        return result
    
    @staticmethod
    def _validate_samples_per_pixel_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Samples per Pixel constraints."""
        if 'SamplesPerPixel' in data:
            if not isinstance(data.SamplesPerPixel, int) or data.SamplesPerPixel < 1:
                result.add_error(
                    f"Samples per Pixel (0028,0002) must be a positive integer, got "
                    f"'{data.SamplesPerPixel}'. Valid values are 1 for monochrome/palette "
                    f"images or 3 for RGB/YBR color images."
                )

    @staticmethod
    def _validate_image_dimensions_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate image dimension constraints."""
        if 'Rows' in data:
            if not isinstance(data.Rows, int) or data.Rows < 1:
                result.add_error(
                    f"Rows (0028,0010) must be a positive integer, got '{data.Rows}'. "
                    f"This represents the number of rows in the image matrix."
                )

        if 'Columns' in data:
            if not isinstance(data.Columns, int) or data.Columns < 1:
                result.add_error(
                    f"Columns (0028,0011) must be a positive integer, got '{data.Columns}'. "
                    f"This represents the number of columns in the image matrix."
                )

    @staticmethod
    def _validate_bit_allocation_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate bit allocation constraints."""
        if 'BitsAllocated' in data:
            if not isinstance(data.BitsAllocated, int) or data.BitsAllocated < 1:
                result.add_error(
                    f"Bits Allocated (0028,0100) must be a positive integer, got "
                    f"'{data.BitsAllocated}'. This specifies bits allocated for each pixel sample."
                )
            elif data.BitsAllocated != 1 and data.BitsAllocated % 8 != 0:
                result.add_error(
                    f"Bits Allocated (0028,0100) must be 1 or a multiple of 8, got "
                    f"'{data.BitsAllocated}'. According to DICOM PS3.5, valid values "
                    f"are 1, 8, 16, 24, 32, etc."
                )

        if 'BitsStored' in data:
            if not isinstance(data.BitsStored, int) or data.BitsStored < 1:
                result.add_error(
                    f"Bits Stored (0028,0101) must be a positive integer, got "
                    f"'{data.BitsStored}'. This specifies the number of bits actually "
                    f"used for each pixel sample."
                )

        if 'HighBit' in data:
            if not isinstance(data.HighBit, int) or data.HighBit < 0:
                result.add_error(
                    f"High Bit (0028,0102) must be a non-negative integer, got "
                    f"'{data.HighBit}'. This specifies the most significant bit position."
                )

    @staticmethod
    def _validate_bit_relationships_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate relationships between bit-related attributes."""
        if all(attr in data for attr in ['BitsAllocated', 'BitsStored', 'HighBit']):
            if data.BitsStored > data.BitsAllocated:
                result.add_error(
                    f"Bits Stored (0028,0101) cannot exceed Bits Allocated (0028,0100). "
                    f"Got Bits Stored = {data.BitsStored}, Bits Allocated = {data.BitsAllocated}. "
                    f"Bits Stored represents the actual bits used, which cannot exceed the allocated space."
                )

            expected_high_bit = data.BitsStored - 1
            if data.HighBit != expected_high_bit:
                result.add_error(
                    f"High Bit (0028,0102) must equal Bits Stored (0028,0101) - 1. "
                    f"Expected {expected_high_bit}, got {data.HighBit}. "
                    f"According to DICOM PS3.3 C.*******, High Bit shall be one less than Bits Stored."
                )
    
    @staticmethod
    def _validate_pixel_data_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate pixel data Type 1C requirements."""
        has_pixel_data = 'PixelData' in data
        has_pixel_data_url = 'PixelDataProviderURL' in data

        if not has_pixel_data and not has_pixel_data_url:
            result.add_error(
                "Missing required pixel data: Either Pixel Data (7FE0,0010) or "
                "Pixel Data Provider URL (0028,7FE0) is required. According to DICOM PS3.3 C.7.6.3, "
                "exactly one of these Type 1C elements must be present. Pixel Data contains the "
                "actual pixel samples, while Pixel Data Provider URL is used for JPIP transfer syntaxes."
            )
        elif has_pixel_data and has_pixel_data_url:
            result.add_error(
                "Both Pixel Data (7FE0,0010) and Pixel Data Provider URL (0028,7FE0) are present. "
                "According to DICOM PS3.3 C.7.6.3, these are mutually exclusive Type 1C elements. "
                "Only one of pixel_data or pixel_data_provider_url should be provided. "
                "DICOM PS3.3 C.7.6.3 requires exactly one of these Type 1C elements."
            )

    @staticmethod
    def _validate_planar_configuration_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate planar configuration Type 1C requirements."""
        if 'SamplesPerPixel' in data:
            try:
                samples_per_pixel = int(data.SamplesPerPixel)
                if samples_per_pixel > 1:
                    if 'PlanarConfiguration' not in data:
                        result.add_error(
                            f"Missing Planar Configuration (0028,0006): Required when Samples per Pixel "
                            f"(0028,0002) > 1. Current Samples per Pixel = {data.SamplesPerPixel}. "
                            f"According to DICOM PS3.3 C.*******, Planar Configuration specifies whether "
                            f"color pixel data are encoded color-by-pixel (0) or color-by-plane (1)."
                        )
                elif samples_per_pixel == 1 and 'PlanarConfiguration' in data:
                    result.add_warning(
                        f"Planar Configuration (0028,0006) should not be present when Samples per Pixel "
                        f"(0028,0002) = 1. According to DICOM PS3.3 C.*******, this attribute shall be "
                        f"present if Samples per Pixel has a value greater than 1, and shall not be "
                        f"present otherwise."
                    )
            except (ValueError, TypeError):
                # Invalid SamplesPerPixel value - will be caught by Type 1 validation
                pass

    @staticmethod
    def _validate_palette_color_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate palette color lookup table Type 1C requirements."""
        if 'PhotometricInterpretation' in data and data.PhotometricInterpretation == "PALETTE COLOR":
            required_palette_attrs = [
                ('RedPaletteColorLookupTableDescriptor', '0028,1101', 'Red palette format descriptor'),
                ('GreenPaletteColorLookupTableDescriptor', '0028,1102', 'Green palette format descriptor'),
                ('BluePaletteColorLookupTableDescriptor', '0028,1103', 'Blue palette format descriptor'),
                ('RedPaletteColorLookupTableData', '0028,1201', 'Red palette lookup table data'),
                ('GreenPaletteColorLookupTableData', '0028,1202', 'Green palette lookup table data'),
                ('BluePaletteColorLookupTableData', '0028,1203', 'Blue palette lookup table data')
            ]

            missing_attrs = []
            for attr_name, tag, _ in required_palette_attrs:
                if attr_name not in data:
                    missing_attrs.append(f"{attr_name} ({tag})")

            if missing_attrs:
                result.add_error(
                    f"Missing required palette color attributes: {', '.join(missing_attrs)}. "
                    f"According to DICOM PS3.3 C.*******, all palette color lookup table "
                    f"attributes are Type 1C - required when Photometric Interpretation = PALETTE COLOR. "
                    f"These define the color mapping for single-sample palette color images."
                )

    @staticmethod
    def _validate_pixel_aspect_ratio_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate pixel aspect ratio Type 1C requirements."""
        # Note: This is a complex conditional that depends on physical spacing attributes
        # from other modules. For now, we validate the format if present.
        if 'PixelAspectRatio' in data:
            if not isinstance(data.PixelAspectRatio, (list, tuple)) or len(data.PixelAspectRatio) != 2:
                result.add_error(
                    f"Pixel Aspect Ratio (0028,0034) must be a sequence of exactly 2 values "
                    f"[vertical_size, horizontal_size], got '{data.PixelAspectRatio}'. "
                    f"According to DICOM PS3.3 C.*******.7, this represents the ratio of "
                    f"vertical to horizontal pixel size as integer values."
                )

    @staticmethod
    def _validate_extended_offset_table_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate extended offset table Type 1C requirements."""
        if 'ExtendedOffsetTable' in data and 'ExtendedOffsetTableLengths' not in data:
            result.add_error(
                "Missing Extended Offset Table Lengths (7FE0,0002): Required when "
                "Extended Offset Table (7FE0,0001) is present. According to DICOM PS3.3 C.7.6.3, "
                "Extended Offset Table Lengths is Type 1C - required if Extended Offset Table "
                "is present. These provide byte lengths of frames in encapsulated pixel data."
            )

        # Validate Extended Offset Table constraints
        if 'ExtendedOffsetTable' in data:
            # According to DICOM PS3.3 C.*******.8, Extended Offset Table may only be present when:
            # - Pixel Data is present, and
            # - Transfer Syntax uses Encapsulated Format, and
            # - Transfer Syntax encodes Frames in separate Fragments, and
            # - Basic Offset Table is not present (first Item has zero length), and
            # - Each Frame is entirely contained within one Fragment
            if 'PixelData' not in data:
                result.add_error(
                    "Extended Offset Table (7FE0,0001) may only be present when Pixel Data "
                    "(7FE0,0010) is present. According to DICOM PS3.3 C.*******.8, Extended "
                    "Offset Table is only used with encapsulated pixel data."
                )

    @staticmethod
    def _validate_pixel_padding_requirements_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate pixel padding range limit Type 1C requirements."""
        if 'PixelPaddingRangeLimit' in data:
            # Note: Pixel Padding Value (0028,0120) is from General Equipment Module
            # We can only warn about this dependency
            result.add_warning(
                "Pixel Padding Range Limit (0028,0121) is present. According to DICOM PS3.3 C.7.6.3, "
                "this Type 1C element requires Pixel Padding Value (0028,0120) from the General "
                "Equipment Module to be present as well. Ensure both attributes are used together "
                "to define a range of padding values."
            )

    @staticmethod
    def _validate_photometric_constraints_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate photometric interpretation constraints on samples per pixel."""

        if 'PhotometricInterpretation' in data and 'SamplesPerPixel' in data:
            photometric = data.PhotometricInterpretation
            samples = data.SamplesPerPixel

            # Single-sample photometric interpretations
            single_sample_types = ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]
            if photometric in single_sample_types and samples != 1:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 1, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent single-plane data and must have exactly 1 sample per pixel."
                )

            # Three-sample photometric interpretations
            three_sample_types = ["RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "XYB"]
            if photometric in three_sample_types and samples != 3:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 3, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent three-component color data and must have exactly 3 samples per pixel."
                )
    
    @staticmethod
    def _validate_ybr_format_constraints_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate YBR format specific constraints."""

        if 'PhotometricInterpretation' not in data:
            return

        photometric = data.PhotometricInterpretation

        # YBR_FULL_422 specific constraints
        if photometric == "YBR_FULL_422":
            # Must have Planar Configuration = 0
            if 'PlanarConfiguration' in data and data.PlanarConfiguration != 0:
                result.add_error(
                    f"YBR_FULL_422 Photometric Interpretation requires Planar Configuration = 0, "
                    f"got {data.PlanarConfiguration}. According to DICOM PS3.3 C.*******.2, "
                    f"YBR_FULL_422 shall have Planar Configuration = 0 for proper chrominance subsampling."
                )

            # Must have even number of columns for horizontal subsampling
            if 'Columns' in data and data.Columns % 2 != 0:
                result.add_error(
                    f"YBR_FULL_422 Photometric Interpretation requires even number of Columns "
                    f"for horizontal subsampling, got {data.Columns}. According to DICOM PS3.3 C.*******.2, "
                    f"YBR_FULL_422 uses horizontal chrominance subsampling which requires even column count."
                )

        # YBR_PARTIAL_420, YBR_ICT, YBR_RCT constraints (compressed format only)
        compressed_only_types = ["YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT"]
        if photometric in compressed_only_types:
            # These should only be used with compressed transfer syntaxes
            # We can only provide a warning since we don't have transfer syntax info
            result.add_warning(
                f"Photometric Interpretation '{photometric}' should only be used with "
                f"compressed transfer syntaxes. According to DICOM PS3.3 C.*******.2, "
                f"{photometric} shall only be used for pixel data in Encapsulated (compressed) format."
            )

            # Must have Planar Configuration = 0
            if 'PlanarConfiguration' in data and data.PlanarConfiguration != 0:
                result.add_error(
                    f"{photometric} Photometric Interpretation requires Planar Configuration = 0, "
                    f"got {data.PlanarConfiguration}. According to DICOM PS3.3 C.*******.2, "
                    f"{photometric} shall have Planar Configuration = 0."
                )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data consistency and format constraints."""

        # Validate palette color descriptor consistency
        ImagePixelValidator._validate_palette_descriptor_consistency(dataset, result)

        # Validate ICC Profile and Color Space constraints
        ImagePixelValidator._validate_color_profile_constraints(dataset, result)

    @staticmethod
    def _validate_photometric_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate photometric interpretation constraints on samples per pixel."""

        if hasattr(dataset, 'PhotometricInterpretation') and hasattr(dataset, 'SamplesPerPixel'):
            photometric = dataset.PhotometricInterpretation
            samples = dataset.SamplesPerPixel

            # Single-sample photometric interpretations
            single_sample_types = ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]
            if photometric in single_sample_types and samples != 1:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 1, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent single-plane data and must have exactly 1 sample per pixel."
                )

            # Three-sample photometric interpretations
            three_sample_types = ["RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "XYB"]
            if photometric in three_sample_types and samples != 3:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 3, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent three-component color data and must have exactly 3 samples per pixel."
                )

    @staticmethod
    def _validate_palette_descriptor_consistency_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate palette color descriptor consistency."""

        if 'PhotometricInterpretation' in data and data.PhotometricInterpretation == "PALETTE COLOR":
            descriptors = []
            descriptor_names = []

            for color in ['Red', 'Green', 'Blue']:
                attr_name = f'{color}PaletteColorLookupTableDescriptor'
                if attr_name in data:
                    descriptors.append(getattr(data, attr_name))
                    descriptor_names.append(attr_name)

            # Validate descriptor format for any present descriptors (should be 3-element sequences)
            for i, (desc, name) in enumerate(zip(descriptors, descriptor_names)):
                # Convert to list if it's a pydicom sequence type
                try:
                    desc_list = list(desc) if hasattr(desc, '__iter__') and not isinstance(desc, str) else desc
                    if not hasattr(desc_list, '__len__') or len(desc_list) != 3:
                        result.add_error(
                            f"{name} must be a list of exactly 3 integers "
                            f"[entries, first_value, bits_per_entry], got '{desc}'. "
                            f"According to DICOM PS3.3 C.*******.5, palette descriptors specify "
                            f"the format of the lookup table data."
                        )
                        continue
                    # Update the descriptor in the list for consistency checking
                    descriptors[i] = desc_list
                except (TypeError, ValueError):
                    result.add_error(
                        f"{name} must be a list of exactly 3 integers "
                        f"[entries, first_value, bits_per_entry], got '{desc}'. "
                        f"According to DICOM PS3.3 C.*******.5, palette descriptors specify "
                        f"the format of the lookup table data."
                    )
                    continue

            if len(descriptors) == 3:

                # Check consistency between descriptors
                if len(descriptors) == 3 and all(hasattr(desc, '__len__') and len(desc) == 3 for desc in descriptors):
                    # First value (number of entries) must be identical
                    if not all(desc[0] == descriptors[0][0] for desc in descriptors):
                        result.add_error(
                            f"All Palette Color Lookup Table Descriptors must have the same first value "
                            f"(number of entries). Got Red={descriptors[0][0]}, Green={descriptors[1][0]}, "
                            f"Blue={descriptors[2][0]}. According to DICOM PS3.3 C.*******.5, the first "
                            f"value shall be identical for each color component."
                        )

                    # Second value (first input value mapped) must be identical
                    if not all(desc[1] == descriptors[0][1] for desc in descriptors):
                        result.add_error(
                            f"All Palette Color Lookup Table Descriptors must have the same second value "
                            f"(first input value mapped). Got Red={descriptors[0][1]}, Green={descriptors[1][1]}, "
                            f"Blue={descriptors[2][1]}. According to DICOM PS3.3 C.*******.5, the second "
                            f"value shall be identical for each color component."
                        )

                    # Third value (bits per entry) must be identical for RGB
                    if not all(desc[2] == descriptors[0][2] for desc in descriptors):
                        result.add_error(
                            f"All RGB Palette Color Lookup Table Descriptors must have the same third value "
                            f"(bits per entry). Got Red={descriptors[0][2]}, Green={descriptors[1][2]}, "
                            f"Blue={descriptors[2][2]}. According to DICOM PS3.3 C.*******.5, the third "
                            f"value shall be identical for RGB components."
                        )

    @staticmethod
    def _validate_color_profile_constraints_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate ICC Profile and Color Space constraints."""

        # ICC Profile constraints
        if 'ICCProfile' in data:
            # Check for Optical Path Sequence constraint
            if 'OpticalPathSequence' in data:
                result.add_error(
                    "ICC Profile (0028,2000) shall not be present when Optical Path Sequence "
                    "(0048,0105) is present. According to DICOM PS3.3 C.*******, when the "
                    "Optical Path Module is used, each optical path has its own ICC Profile."
                )

        # Color Space constraints
        if 'ColorSpace' in data:
            # Should be consistent with ICC Profile if present
            if 'ICCProfile' in data:
                result.add_warning(
                    "Color Space (0028,2002) is present along with ICC Profile (0028,2000). "
                    "According to DICOM PS3.3 C.*******, Color Space shall be consistent "
                    "with any ICC Profile that is also present. Verify color space compatibility."
                )

            # Should not be present when Optical Path Sequence present
            if 'OpticalPathSequence' in data:
                result.add_error(
                    "Color Space (0028,2002) shall not be present when Optical Path Sequence "
                    "(0048,0105) is present. According to DICOM PS3.3 C.*******, color space "
                    "information is handled per optical path when the Optical Path Module is used."
                )

    @staticmethod
    def _validate_palette_color_conditional_logic_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate palette color conditional logic (when palette attributes present with wrong photometric)."""
        if 'PhotometricInterpretation' in data:
            photometric = data.PhotometricInterpretation

            # Check palette color descriptors
            palette_attrs = [
                'RedPaletteColorLookupTableDescriptor',
                'GreenPaletteColorLookupTableDescriptor',
                'BluePaletteColorLookupTableDescriptor'
            ]

            has_palette_attrs = any(attr in data for attr in palette_attrs)

            if photometric != "PALETTE COLOR" and has_palette_attrs:
                result.add_error(
                    f"Palette Color Lookup Tables are only required when "
                    f"Photometric Interpretation = PALETTE COLOR, but got "
                    f"'{photometric}'. According to DICOM PS3.3 C.7.6.3, palette "
                    f"color lookup table attributes should not be present for other "
                    f"photometric interpretations."
                )
