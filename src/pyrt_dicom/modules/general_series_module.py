"""
General Series Module - DICOM PS3.3 C.7.3.1

The General Series Module contains attributes that identify and describe 
the Series performed as part of a Study.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ..enums.series_enums import Modality, Laterality, PatientPosition, AnatomicalOrientationType
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.general_series_validator import GeneralSeriesValidator
from ..validators import ValidationResult, ValidationError
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class GeneralSeriesModule(BaseModule):
    """General Series Module implementation for DICOM PS3.3 C.7.3.1.
    
    Uses composition with internal dataset management for clean separation of concerns.
    Contains attributes that identify and describe the Series performed as part of a Study.
    
    Usage:
        # Create with required elements using enums for type safety
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,  # Use enum instead of string "CT"
            series_instance_uid="*******.*******.9.10",
            series_number=1
        )
        
        # Add optional elements with date/time formatting
        series.with_optional_elements(
            series_date="20240101",  # YYYYMMDD format or datetime object
            series_time="120000",    # HHMMSS format or datetime object
            series_description="Chest CT with contrast",
            body_part_examined="CHEST"
        )
        
        # Add conditional positioning (Type 2C) - required for certain modalities
        series.with_patient_positioning(
            patient_position=PatientPosition.HFS  # Head First Supine
        )
        
        # Add conditional laterality (Type 2C for paired body parts)
        series.with_laterality_information(
            laterality=Laterality.LEFT,    # Use enum for consistency
            body_part_examined="BREAST"    # Paired body part
        )
        
        # Enum usage provides IntelliSense and validation
        assert series['Modality'] == Modality.CT.value  # "CT"
        assert series['PatientPosition'] == PatientPosition.HFS.value  # "HFS"
        
        # Generate dataset for IOD integration
        dataset = series.to_dataset()
        
        # Validate with structured results
        result = series.validate()  # Returns ValidationResult object
    """

    def __init__(self):
        """Initialize GeneralSeriesModule with internal dataset and validation flags."""
        super().__init__()

    @classmethod
    def from_required_elements(
        cls,
        modality: str | Modality,
        series_instance_uid: str,
        series_number: int | None = None
    ) -> 'GeneralSeriesModule':
        """Create GeneralSeriesModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            modality (str | Modality): Type of equipment that originally acquired the data (0008,0060) Type 1.
                Use Modality enum for type safety and IntelliSense: Modality.CT, Modality.MR, Modality.RTDOSE, etc.
                String values like "CT", "MR" are also accepted for compatibility.
            series_instance_uid (str): Unique identifier for the Series (0020,000E) Type 1.
                Must be a valid DICOM UID format (e.g., "1.2.840.10008.*******.5").
            series_number (int | None): Series number (0020,0011) Type 2.
                Integer identifier for series ordering, can be None for empty Type 2.
            
        Returns:
            GeneralSeriesModule: New module instance with required data elements set
        """
        instance = cls()
        instance['Modality'] = format_enum_string(modality)
        instance['SeriesInstanceUID'] = series_instance_uid
        instance['SeriesNumber'] = series_number
        return instance
    
    def with_optional_elements(
        self,
        series_date: str | datetime | date | None = None,
        series_time: str | datetime | None = None,
        performing_physicians_name: str | PersonName | None = None,
        performing_physician_identification_sequence: list[Dataset] | None = None,
        operators_name: str | PersonName | None = None,
        operator_identification_sequence: list[Dataset] | None = None,
        referenced_performed_procedure_step_sequence: list[Dataset] | None = None,
        related_series_sequence: list[Dataset] | None = None,
        series_description: str | None = None,
        series_description_code_sequence: list[Dataset] | None = None,
        protocol_name: str | None = None,
        body_part_examined: str | None = None,
        smallest_pixel_value_in_series: int | None = None,
        largest_pixel_value_in_series: int | None = None,
        request_attributes_sequence: list[Dataset] | None = None,
        treatment_session_uid: str | None = None
    ) -> 'GeneralSeriesModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            series_date (str | datetime | date | None): Date the Series started (0008,0021) Type 3
            series_time (str | datetime | None): Time the Series started (0008,0031) Type 3
            performing_physicians_name (str | PersonName | None): Name of physician performing the Series (0008,1050) Type 3
            performing_physician_identification_sequence (list[Dataset] | None): Performing physician ID (0008,1052) Type 3
            operators_name (str | PersonName | None): Name of operator of equipment (0008,1070) Type 3
            operator_identification_sequence (list[Dataset] | None): Operator identification (0008,1072) Type 3
            referenced_performed_procedure_step_sequence (list[Dataset] | None): Reference to performed procedure step (0008,1111) Type 3
            related_series_sequence (list[Dataset] | None): Related Series (0008,1250) Type 3
            series_description (str | None): User provided description of the Series (0008,103E) Type 3
            series_description_code_sequence (list[Dataset] | None): Coded series description (0008,103F) Type 3
            protocol_name (str | None): User or equipment generated identifier (0018,1030) Type 3
            body_part_examined (str | None): Text description of part of body examined (0018,0015) Type 3
            smallest_pixel_value_in_series (int | None): Smallest pixel value in Series (0028,0108) Type 3
            largest_pixel_value_in_series (int | None): Largest pixel value in Series (0028,0109) Type 3
            request_attributes_sequence (list[Dataset] | None): Attributes from the Imaging Service Request (0040,0275) Type 3
            treatment_session_uid (str | None): Unique identifier of RT Treatment Session (300A,0700) Type 3
            
        Returns:
            GeneralSeriesModule: Self with optional elements added
        """
        if series_date is not None:
            self.SeriesDate = format_date_value(series_date)
        if series_time is not None:
            self.SeriesTime = format_time_value(series_time)
        if performing_physicians_name is not None:
            self.PerformingPhysicianName = performing_physicians_name
        if performing_physician_identification_sequence is not None:
            self.PerformingPhysicianIdentificationSequence = performing_physician_identification_sequence
        if operators_name is not None:
            self.OperatorsName = operators_name
        if operator_identification_sequence is not None:
            self.OperatorIdentificationSequence = operator_identification_sequence
        if referenced_performed_procedure_step_sequence is not None:
            self.ReferencedPerformedProcedureStepSequence = referenced_performed_procedure_step_sequence
        if related_series_sequence is not None:
            self.RelatedSeriesSequence = related_series_sequence
        if series_description is not None:
            self.SeriesDescription = series_description
        if series_description_code_sequence is not None:
            self.SeriesDescriptionCodeSequence = series_description_code_sequence
        if protocol_name is not None:
            self.ProtocolName = protocol_name
        if body_part_examined is not None:
            self.BodyPartExamined = body_part_examined
        if smallest_pixel_value_in_series is not None:
            self.SmallestPixelValueInSeries = smallest_pixel_value_in_series
        if largest_pixel_value_in_series is not None:
            self.LargestPixelValueInSeries = largest_pixel_value_in_series
        if request_attributes_sequence is not None:
            self.RequestAttributesSequence = request_attributes_sequence
        if treatment_session_uid is not None:
            self.TreatmentSessionUID = treatment_session_uid
        return self
    
    def with_patient_positioning(
        self,
        patient_position: str | PatientPosition | None = None,
        sop_class_uid: str | None = None
    ) -> 'GeneralSeriesModule':
        """Add patient positioning information (Type 2C).
        
        Patient Position is Type 2C: Required for images where Patient Orientation Code Sequence 
        (0054,0410) is not present AND whose SOP Class UID (0008,0016) is one of:
        - CT Image Storage, MR Image Storage, Enhanced CT Image Storage, 
        - Enhanced MR Image Storage, Enhanced Color MR Image Storage, MR Spectroscopy Storage
        
        Args:
            patient_position (str | PatientPosition | None): Patient position descriptor (0018,5100) Type 2C
            sop_class_uid (str | None): SOP Class UID for conditional validation
            
        Returns:
            GeneralSeriesModule: Self with positioning elements added
        """
        if patient_position is not None:
            self.PatientPosition = format_enum_string(patient_position)
        if sop_class_uid is not None:
            self.SOPClassUID = sop_class_uid
        return self
    
    def with_laterality_information(
        self,
        laterality: str | Laterality | None = None,
        body_part_examined: str | None = None
    ) -> 'GeneralSeriesModule':
        """Add laterality information (Type 2C).
        
        Laterality is Type 2C: Required if the body part examined is a paired structure
        AND Image Laterality (0020,0062) or Frame Laterality (0020,9072) or 
        Measurement Laterality (0024,0113) are not present.
        
        DICOM PS3.3 C.7.3.1: "Required if the body part examined is a paired structure 
        and Image Laterality (0020,0062) or Frame Laterality (0020,9072) or 
        Measurement Laterality (0024,0113) are not present."
        
        Args:
            laterality (str | Laterality | None): Laterality of body part examined (0020,0060) Type 2C.
                Enumerated Values: R (right), L (left)
            body_part_examined (str | None): Body part for conditional validation
            
        Returns:
            GeneralSeriesModule: Self with laterality elements added
        """
        if body_part_examined is not None:
            self.BodyPartExamined = body_part_examined
        
        if laterality is not None:
            self.Laterality = format_enum_string(laterality)
        return self
    
    def with_anatomical_orientation(
        self,
        anatomical_orientation_type: str | AnatomicalOrientationType | None = None
    ) -> 'GeneralSeriesModule':
        """Add anatomical orientation information (Type 1C).
        
        Anatomical Orientation Type is Type 1C: Required if the Patient is a non-human organism 
        and the anatomical Frame of Reference is not bipedal. See patient_module.py.
        
        DICOM PS3.3 C.7.3.1: "Required if the Patient is a non-human organism and 
        the anatomical Frame of Reference is not bipedal. May be present otherwise."
        
        Args:
            anatomical_orientation_type (str | AnatomicalOrientationType | None): Anatomical orientation (0010,2210) Type 1C.
                Enumerated Values: BIPED, QUADRUPED
            
        Returns:
            GeneralSeriesModule: Self with anatomical orientation elements added
            
        Notes:
            - If this attribute is not present, the default human standard anatomical 
              position is used to define the patient orientation.
            - For quadrupeds, separate concepts for ventral and dorsal are not introduced.
        """
        if anatomical_orientation_type is not None:
            self.AnatomicalOrientationType = format_enum_string(anatomical_orientation_type)
        
        return self
    
    def with_operator_information(
        self,
        operators_name: str | PersonName | None = None,
        operator_identification_sequence: list[Dataset] | None = None,
        performing_physicians_name: str | PersonName | None = None,
        performing_physician_identification_sequence: list[Dataset] | None = None
    ) -> 'GeneralSeriesModule':
        """Add operator and performing physician information.
        
        Args:
            operators_name (str | PersonName | None): Name of operator of equipment (0008,1070) Type 3
            operator_identification_sequence (list[Dataset] | None): Operator identification (0008,1072) Type 3
            performing_physicians_name (str | PersonName | None): Name of physician performing the Series (0008,1050) Type 3
            performing_physician_identification_sequence (list[Dataset] | None): Performing physician ID (0008,1052) Type 3
            
        Returns:
            GeneralSeriesModule: Self with operator information elements added
        """
        if operators_name is not None:
            self.OperatorsName = operators_name
        if operator_identification_sequence is not None:
            self.OperatorIdentificationSequence = operator_identification_sequence
        if performing_physicians_name is not None:
            self.PerformingPhysicianName = performing_physicians_name
        if performing_physician_identification_sequence is not None:
            self.PerformingPhysicianIdentificationSequence = performing_physician_identification_sequence
        return self

    @staticmethod
    def create_related_series_item(
        study_instance_uid: str,
        series_instance_uid: str,
        purpose_of_reference_code_sequence: list[Dataset]
    ) -> Dataset:
        """Create an item for Related Series Sequence (0008,1140).

        Args:
            study_instance_uid (str): Study Instance UID of related series
            series_instance_uid (str): Series Instance UID of related series
            purpose_of_reference_code_sequence (list[Dataset]): Purpose of reference

        Returns:
            Dataset: Sequence item with related series information
        """
        item = Dataset()
        item.StudyInstanceUID = study_instance_uid
        item.SeriesInstanceUID = series_instance_uid
        item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        return item

    @staticmethod
    def create_referenced_performed_procedure_step_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> Dataset:
        """Create an item for Referenced Performed Procedure Step Sequence (0008,1111).

        Args:
            referenced_sop_class_uid (str): SOP Class UID of referenced procedure step
            referenced_sop_instance_uid (str): SOP Instance UID of referenced procedure step

        Returns:
            Dataset: Sequence item with procedure step reference
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        return item

    @staticmethod  
    def create_request_attributes_item(
        scheduled_procedure_step_id: str,
        scheduled_procedure_step_description: str | None = None,
        requested_procedure_id: str | None = None,
        requested_procedure_description: str | None = None
    ) -> Dataset:
        """Create an item for Request Attributes Sequence (0040,0275).
        
        Contains attributes from the Imaging Service Request as specified in 
        DICOM PS3.3 C.7.3.1.
        
        Args:
            scheduled_procedure_step_id (str): Scheduled Procedure Step ID (0040,0009)
            scheduled_procedure_step_description (str | None): Description (0040,0007)
            requested_procedure_id (str | None): Requested Procedure ID (0040,1001) 
            requested_procedure_description (str | None): Description (0040,1002)
            
        Returns:
            Dataset: Sequence item with request attributes
        """
        item = Dataset()
        item.ScheduledProcedureStepID = scheduled_procedure_step_id
        if scheduled_procedure_step_description is not None:
            item.ScheduledProcedureStepDescription = scheduled_procedure_step_description
        if requested_procedure_id is not None:
            item.RequestedProcedureID = requested_procedure_id
        if requested_procedure_description is not None:
            item.RequestedProcedureDescription = requested_procedure_description
        return item

    @property
    def has_operator_info(self) -> bool:
        """Check if operator information is present.

        Returns:
            bool: True if operator-related elements are present
        """
        return any(attr in self for attr in [
            'OperatorsName', 'OperatorIdentificationSequence'
        ])

    @property
    def has_performing_physician_info(self) -> bool:
        """Check if performing physician information is present.

        Returns:
            bool: True if performing physician elements are present
        """
        return any(attr in self for attr in [
            'PerformingPhysicianName', 'PerformingPhysicianIdentificationSequence'
        ])

    @property
    def has_positioning_info(self) -> bool:
        """Check if patient positioning information is present.

        Returns:
            bool: True if positioning elements are present
        """
        return any(attr in self for attr in [
            'PatientPosition', 'Laterality', 'AnatomicalOrientationType'
        ])

    @property
    def has_pixel_value_info(self) -> bool:
        """Check if pixel value information is present.

        Returns:
            bool: True if pixel value elements are present
        """
        return any(attr in self for attr in [
            'SmallestPixelValueInSeries', 'LargestPixelValueInSeries'
        ])
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured with required elements.
        
        Returns:
            bool: True if all Type 1 elements are present
        """
        return ('Modality' in self and 
                'SeriesInstanceUID' in self)
    
    @property
    def requires_patient_position(self) -> bool:
        """Check if Patient Position is required based on SOP Class UID.
        
        Returns:
            bool: True if Patient Position is required (Type 2C)
        """
        sop_class_uid = self.SOPClassUID if 'SOPClassUID' in self else ''
        required_sop_classes = [
            "1.2.840.10008.5.1.4.1.1.2",    # CT Image Storage
            "1.2.840.10008.5.1.4.1.1.4",    # MR Image Storage
            "1.2.840.10008.5.1.4.1.1.2.1",  # Enhanced CT Image Storage
            "1.2.840.10008.5.1.4.1.1.4.1",  # Enhanced MR Image Storage
            "1.2.840.10008.5.1.4.1.1.4.3",  # Enhanced Color MR Image Storage
            "1.2.840.10008.5.1.4.1.1.4.2"   # MR Spectroscopy Storage
        ]
        
        if sop_class_uid in required_sop_classes:
            return 'PatientOrientationCodeSequence' not in self
        return False
    
    @property
    def requires_laterality(self) -> bool:
        """Check if Laterality is required based on body part examined.
        
        Returns:
            bool: True if Laterality is required (Type 2C)
        """
        if 'BodyPartExamined' not in self:
            return False
            
        body_part = self.BodyPartExamined
        paired_body_parts = {
            'BREAST', 'EYE', 'KIDNEY', 'LUNG', 'OVARY', 'TESTIS', 
            'ARM', 'LEG', 'HAND', 'FOOT', 'SHOULDER', 'ELBOW', 
            'WRIST', 'HIP', 'KNEE', 'ANKLE', 'EAR', 'ADRENAL',
            'UTERINE_TUBE', 'OVARIAN'
        }
        
        is_paired_structure = any(
            part.upper() in body_part.upper() or
            body_part.upper() in part
            for part in paired_body_parts
        )
        
        if is_paired_structure:
            has_other_laterality = any(attr in self for attr in 
                                     ['ImageLaterality', 'FrameLaterality', 'MeasurementLaterality'])
            return not has_other_laterality
        return False

    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return GeneralSeriesValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return GeneralSeriesValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return GeneralSeriesValidator.validate_enumerated_values(self)
    
    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return GeneralSeriesValidator.validate_sequence_structures(self)
    
    def check_cross_field_dependencies(self) -> ValidationResult:
        """Check cross-field dependencies and logical relationships with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid cross-field relationships
        """
        return GeneralSeriesValidator.validate_cross_field_dependencies(self)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return GeneralSeriesValidator.validate(self, config)  # Direct self reference for performance
    
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.
        
        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            GeneralSeriesValidator.validate_required_elements,
            "Required elements validation failed"
        )
    
    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            GeneralSeriesValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            GeneralSeriesValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
    
    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            GeneralSeriesValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
    
    def _ensure_cross_field_dependencies_valid(self) -> None:
        """Ensure cross-field dependencies are valid, raise exception if not.
        
        Raises:
            ValidationError: If cross-field dependencies are invalid
        """
        self._validate_and_raise(
            GeneralSeriesValidator.validate_cross_field_dependencies,
            "Cross-field dependencies validation failed"
        )
