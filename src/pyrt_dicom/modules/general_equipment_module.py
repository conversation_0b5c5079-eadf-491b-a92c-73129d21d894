"""
General Equipment Module - DICOM PS3.3 C.7.5.1

The General Equipment Module identifies and describes the piece of equipment
that produced Composite Instances.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.equipment_enums import Manufacturer
from ..validators.modules.general_equipment_validator import GeneralEquipmentValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_enum_string


class GeneralEquipmentModule(BaseModule):
    """General Equipment Module implementation for DICOM PS3.3 C.7.5.1.

    Uses composition with internal dataset management for clean separation of concerns.
    Identifies and describes the piece of equipment that produced Composite Instances.

    Usage:
        # Create with required elements using standardized manufacturer enum
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer=Manufacturer.VARIAN  # Use enum for standardization
        )

        # Or use string value for compatibility
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        )

        # Add optional elements
        equipment.with_optional_elements(
            institution_name="General Hospital",
            station_name="CT01",
            manufacturers_model_name="SuperScan 3000",
            device_serial_number="12345",
            software_versions=["v2.1.0", "kernel-1.5.2"]
        )

        # Add conditional pixel padding if needed
        equipment.with_pixel_padding(
            pixel_padding_value=0
        )

        # Generate dataset for IOD integration
        dataset = equipment.to_dataset()

        # Validate
        result = equipment.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        manufacturer: str | Manufacturer = ""
    ) -> 'GeneralEquipmentModule':
        """Create GeneralEquipmentModule from all required (Type 2) data elements.

        Args:
            manufacturer (str | Manufacturer): Manufacturer of the equipment (0008,0070) Type 2.
                Required but may be empty. Use Manufacturer enum for standardized values:
                Manufacturer.VARIAN, Manufacturer.SIEMENS, etc. String values are also accepted.

        Returns:
            GeneralEquipmentModule: New module instance with required data elements set
        """
        instance = cls()
        instance['Manufacturer'] = format_enum_string(manufacturer)
        return instance
    
    def with_optional_elements(
        self,
        institution_name: str | None = None,
        institution_address: str | None = None,
        station_name: str | None = None,
        institutional_department_name: str | None = None,
        institutional_department_type_code_sequence: list[Dataset] | None = None,
        manufacturers_model_name: str | None = None,
        manufacturers_device_class_uid: list[str] | None = None,
        device_serial_number: str | None = None,
        software_versions: list[str] | None = None,
        gantry_id: str | None = None,
        udi_sequence: list[Dataset] | None = None,
        device_uid: str | None = None,
        spatial_resolution: float | None = None,
        date_of_manufacture: str | datetime | date | None = None,
        date_of_installation: str | datetime | date | None = None,
        date_of_last_calibration: list[str] | None = None,
        time_of_last_calibration: list[str] | None = None
    ) -> 'GeneralEquipmentModule':
        """Add optional (Type 3) elements.

        Args:
            institution_name (str | None): Institution where equipment is located (0008,0080) Type 3
            institution_address (str | None): Mailing address of institution (0008,0081) Type 3
            station_name (str | None): User defined name identifying machine (0008,1010) Type 3
            institutional_department_name (str | None): Department in institution (0008,1040) Type 3
            institutional_department_type_code_sequence (list[Dataset] | None): Coded department type (0008,1041) Type 3
            manufacturers_model_name (str | None): Manufacturer's model name (0008,1090) Type 3
            manufacturers_device_class_uid (list[str] | None): Manufacturer's device class UID (0018,100B) Type 3
            device_serial_number (str | None): Manufacturer's serial number (0018,1000) Type 3
            software_versions (list[str] | None): Software version designation (0018,1020) Type 3
            gantry_id (str | None): Identifier of gantry or positioner (0018,1008) Type 3
            udi_sequence (list[Dataset] | None): Unique Device Identifier sequence (0018,100A) Type 3
            device_uid (str | None): Unique identifier of equipment (0018,1002) Type 3
            spatial_resolution (float | None): Inherent limiting resolution in mm (0018,1050) Type 3
            date_of_manufacture (str | datetime | date | None): Date equipment manufactured (0018,1204) Type 3
            date_of_installation (str | datetime | date | None): Date equipment installed (0018,1205) Type 3
            date_of_last_calibration (list[str] | None): Date of last calibration (0018,1200) Type 3
            time_of_last_calibration (list[str] | None): Time of last calibration (0018,1201) Type 3

        Returns:
            GeneralEquipmentModule: Self for method chaining
        """
        if institution_name is not None:
            self.InstitutionName = institution_name
        if institution_address is not None:
            self.InstitutionAddress = institution_address
        if station_name is not None:
            self.StationName = station_name
        if institutional_department_name is not None:
            self.InstitutionalDepartmentName = institutional_department_name
        if institutional_department_type_code_sequence is not None:
            self.InstitutionalDepartmentTypeCodeSequence = institutional_department_type_code_sequence
        if manufacturers_model_name is not None:
            self.ManufacturerModelName = manufacturers_model_name
        if manufacturers_device_class_uid is not None:
            self.ManufacturerDeviceClassUID = manufacturers_device_class_uid
        if device_serial_number is not None:
            self.DeviceSerialNumber = device_serial_number
        if software_versions is not None:
            self.SoftwareVersions = software_versions
        if gantry_id is not None:
            self.GantryID = gantry_id
        if udi_sequence is not None:
            self.UDISequence = udi_sequence
        if device_uid is not None:
            self.DeviceUID = device_uid
        if spatial_resolution is not None:
            self.SpatialResolution = spatial_resolution

        if date_of_manufacture is not None:
            self.DateOfManufacture = format_date_value(date_of_manufacture)
        if date_of_installation is not None:
            self.DateOfInstallation = format_date_value(date_of_installation)

        if date_of_last_calibration is not None:
            self.DateOfLastCalibration = date_of_last_calibration
        if time_of_last_calibration is not None:
            self.TimeOfLastCalibration = time_of_last_calibration

        return self
    
    def with_pixel_padding(
        self,
        pixel_padding_value: int | float
    ) -> 'GeneralEquipmentModule':
        """Add pixel padding value (Type 1C).

        Type 1C: Required if Pixel Padding Range Limit (0028,0121) is present
        and either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present.
        May be present otherwise only if Pixel Data or Pixel Data Provider URL is present.

        Args:
            pixel_padding_value (int | float): Single pixel value used for padding (0028,0120) Type 1C

        Returns:
            GeneralEquipmentModule: Self for method chaining

        Note:
            Use validate() to check for proper usage.
        """
        self.PixelPaddingValue = pixel_padding_value
        return self
    
    @staticmethod
    def create_institutional_department_type_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None
    ) -> Dataset:
        """Create institutional department type code sequence item.

        Args:
            code_value (str): Code value
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            coding_scheme_version (str | None): Coding scheme version

        Returns:
            Dataset: Institutional department type code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        return item
    
    @staticmethod
    def create_udi_item(
        unique_device_identifier: str,
        device_identifier: str | None = None,
        issuer_of_udi: str | None = None,
        udi_issuer_country_code: str | None = None
    ) -> Dataset:
        """Create UDI sequence item.

        Args:
            unique_device_identifier (str): Unique Device Identifier
            device_identifier (str | None): Device Identifier
            issuer_of_udi (str | None): Issuer of UDI
            udi_issuer_country_code (str | None): UDI Issuer Country Code

        Returns:
            Dataset: UDI sequence item
        """
        item = Dataset()
        item.UniqueDeviceIdentifier = unique_device_identifier
        if device_identifier is not None:
            item.DeviceIdentifier = device_identifier
        if issuer_of_udi is not None:
            item.IssuerOfUDI = issuer_of_udi
        if udi_issuer_country_code is not None:
            item.UDIIssuerCountryCode = udi_issuer_country_code
        return item
    
    @property
    def has_institution_info(self) -> bool:
        """Check if institution information is present.

        Returns:
            bool: True if institution name or address is present
        """
        return ('InstitutionName' in self or
                'InstitutionAddress' in self)

    @property
    def has_device_identification(self) -> bool:
        """Check if device identification is present.

        Returns:
            bool: True if model name, serial number, or device UID is present
        """
        return ('ManufacturerModelName' in self or
                'DeviceSerialNumber' in self or
                'DeviceUID' in self)

    @property
    def has_calibration_info(self) -> bool:
        """Check if calibration information is present.

        Returns:
            bool: True if calibration date or time is present
        """
        return ('DateOfLastCalibration' in self or
                'TimeOfLastCalibration' in self)

    @property
    def has_pixel_padding(self) -> bool:
        """Check if pixel padding information is present.

        Returns:
            bool: True if pixel padding value is present
        """
        return 'PixelPaddingValue' in self

    @property
    def requires_pixel_padding(self) -> bool:
        """Check if pixel padding value is required (Type 1C condition).

        Returns:
            bool: True if Type 1C conditions are met requiring pixel padding value
        """
        has_pixel_padding_range_limit = 'PixelPaddingRangeLimit' in self
        has_pixel_data = ('PixelData' in self or
                         'PixelDataProviderURL' in self)
        return has_pixel_padding_range_limit and has_pixel_data

    @property
    def has_calibration_pairing(self) -> bool:
        """Check if calibration date and time are properly paired.

        Returns:
            bool: True if both date and time have same number of values
        """
        date_calibration = self.DateOfLastCalibration if 'DateOfLastCalibration' in self else []
        time_calibration = self.TimeOfLastCalibration if 'TimeOfLastCalibration' in self else []

        if not date_calibration and not time_calibration:
            return True  # No calibration info is fine
        if date_calibration and not time_calibration:
            return True  # Date alone is allowed
        if not date_calibration and time_calibration:
            return False  # Time without date is not allowed

        # Both present - check if they have same number of values
        return len(date_calibration) == len(time_calibration)

    # Public validation convenience methods with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return GeneralEquipmentValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()

    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return GeneralEquipmentValidator.validate_conditional_requirements(self)

    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return GeneralEquipmentValidator.validate_enumerated_values(self)

    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return GeneralEquipmentValidator.validate_sequence_structures(self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult: Validation result containing comprehensive validation results
        """
        return GeneralEquipmentValidator.validate(self, config)  # Direct self reference for performance

    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.

        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            GeneralEquipmentValidator.validate_required_elements,
            "Required elements validation failed"
        )

    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.

        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            GeneralEquipmentValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )

    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.

        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            GeneralEquipmentValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )

    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.

        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            GeneralEquipmentValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
