"""Tests for General Reference Module Validator - DICOM PS3.3 C.12.4"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_reference_validator import GeneralReferenceValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.enums.common_enums import SpatialLocationsPreserved


class TestGeneralReferenceValidator:
    """Test class for GeneralReferenceValidator following pytest framework requirements."""
    
    def test_validate_empty_dataset_passes(self):
        """Test that empty dataset passes validation (all elements are Type 3)."""
        dataset = Dataset()
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_with_derivation_description_only_warns(self):
        """Test that derivation description without source sequences generates warning."""
        dataset = Dataset()
        dataset.DerivationDescription = "Contrast enhanced image"
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Derivation Description" in result.warnings[0]
        assert "Source Image Sequence" in result.warnings[0]
    
    def test_validate_source_image_sequence_basic_structure(self):
        """Test validation of Source Image Sequence basic structure requirements."""
        dataset = Dataset()
        
        # Create source image sequence with missing required elements
        source_item = Dataset()
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        assert any("Referenced SOP Class UID (0008,1150) is required" in error for error in result.errors)
        assert any("Referenced SOP Instance UID (0008,1155) is required" in error for error in result.errors)
    
    def test_validate_source_image_sequence_valid_structure(self):
        """Test validation of Source Image Sequence with valid structure."""
        dataset = Dataset()
        
        # Create valid source image sequence item
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        # Should have warning about source sequence without derivation info
        assert len(result.warnings) == 1
        assert "Source Image Sequence" in result.warnings[0]
    
    def test_validate_spatial_locations_preserved_enumerated_values(self):
        """Test validation of Spatial Locations Preserved enumerated values."""
        dataset = Dataset()
        
        # Create source image sequence with invalid spatial locations preserved value
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "INVALID_VALUE"
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) >= 1
        assert any("should be one of: YES, NO, REORIENTED_ONLY" in warning for warning in result.warnings)
    
    def test_validate_spatial_locations_preserved_valid_values(self):
        """Test validation of valid Spatial Locations Preserved values."""
        dataset = Dataset()
        
        for valid_value in ["YES", "NO", "REORIENTED_ONLY"]:
            source_item = Dataset()
            source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
            source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
            source_item.SpatialLocationsPreserved = valid_value
            dataset.SourceImageSequence = [source_item]
            
            result = GeneralReferenceValidator.validate(dataset)
            
            # Should not have enumerated value warnings
            enum_warnings = [w for w in result.warnings if "should be one of" in w]
            assert len(enum_warnings) == 0
    
    def test_validate_type1c_patient_orientation_missing_fails(self):
        """Test Type 1C validation: Patient Orientation required when Spatial Locations Preserved is REORIENTED_ONLY."""
        dataset = Dataset()
        
        # Create source image sequence with REORIENTED_ONLY but missing Patient Orientation
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "REORIENTED_ONLY"
        # Missing PatientOrientation - should cause error
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Patient Orientation (0020,0020) is Type 1C" in result.errors[0]
        assert "REORIENTED_ONLY" in result.errors[0]
    
    def test_validate_type1c_patient_orientation_present_passes(self):
        """Test Type 1C validation: Patient Orientation present when Spatial Locations Preserved is REORIENTED_ONLY."""
        dataset = Dataset()
        
        # Create source image sequence with REORIENTED_ONLY and Patient Orientation
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "REORIENTED_ONLY"
        source_item.PatientOrientation = ["L", "P"]
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        # Should not have Type 1C errors
        type1c_errors = [e for e in result.errors if "Type 1C" in e]
        assert len(type1c_errors) == 0
    
    def test_validate_patient_orientation_invalid_format_fails(self):
        """Test validation of Patient Orientation format (must be list of 2 values)."""
        dataset = Dataset()
        
        # Create source image sequence with invalid Patient Orientation format
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "REORIENTED_ONLY"
        source_item.PatientOrientation = "L"  # Should be list of 2 values
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "must be a list/array of exactly 2 values" in result.errors[0]
    
    def test_validate_referenced_instance_sequence_missing_purpose_fails(self):
        """Test Type 1 validation: Purpose of Reference Code Sequence required in Referenced Instance Sequence."""
        dataset = Dataset()
        
        # Create referenced instance sequence without required Purpose of Reference Code Sequence
        ref_instance_item = Dataset()
        ref_instance_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"
        ref_instance_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        # Missing PurposeOfReferenceCodeSequence - should cause error
        dataset.ReferencedInstanceSequence = [ref_instance_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Purpose of Reference Code Sequence (0040,A170) is Type 1" in result.errors[0]
    
    def test_validate_referenced_instance_sequence_valid_structure(self):
        """Test validation of Referenced Instance Sequence with valid structure."""
        dataset = Dataset()
        
        # Create valid referenced instance sequence item
        ref_instance_item = Dataset()
        ref_instance_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"
        ref_instance_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        
        # Add required Purpose of Reference Code Sequence
        purpose_item = Dataset()
        purpose_item.CodeValue = "121322"
        purpose_item.CodingSchemeDesignator = "DCM"
        purpose_item.CodeMeaning = "Source for processing"
        ref_instance_item.PurposeOfReferenceCodeSequence = [purpose_item]
        
        dataset.ReferencedInstanceSequence = [ref_instance_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_semantic_constraint_images_in_source_instance_sequence_fails(self):
        """Test semantic validation: Images shall not be referenced by Source Instance Sequence."""
        dataset = Dataset()
        
        # Create source instance sequence with image SOP class (should fail)
        source_instance_item = Dataset()
        source_instance_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        source_instance_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        dataset.SourceInstanceSequence = [source_instance_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Images shall NOT be referenced by Source Instance Sequence" in result.errors[0]
        assert "Use Source Image Sequence" in result.errors[0]
    
    def test_validate_configuration_options(self):
        """Test that validation configuration options work correctly."""
        dataset = Dataset()
        
        # Create dataset with enumerated value issue
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "INVALID_VALUE"
        dataset.SourceImageSequence = [source_item]
        
        # Test with enumerated value checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = GeneralReferenceValidator.validate(dataset, config)
        
        # Should not have enumerated value warnings
        enum_warnings = [w for w in result.warnings if "should be one of" in w]
        assert len(enum_warnings) == 0
    
    def test_validate_multiple_issues_handling(self):
        """Test that validator correctly handles multiple validation issues."""
        dataset = Dataset()
        
        # Create dataset with multiple issues
        source_item = Dataset()
        # Missing required elements
        source_item.SpatialLocationsPreserved = "REORIENTED_ONLY"
        # Missing PatientOrientation (Type 1C error)
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID (structure errors)
        dataset.SourceImageSequence = [source_item]
        
        result = GeneralReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 3  # Type 1C + 2 structure errors
        
        # Verify all expected errors are present
        error_text = " ".join(result.errors)
        assert "Patient Orientation" in error_text
        assert "Referenced SOP Class UID" in error_text
        assert "Referenced SOP Instance UID" in error_text

    # Tests for new granular validation methods with Dataset and BaseModule
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with Dataset instance."""
        dataset = Dataset()

        # All elements are Type 3 in General Reference Module
        result = GeneralReferenceValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No required elements in this module
        assert result.is_valid

    def test_validate_required_elements_with_base_module(self):
        """Test validate_required_elements method with BaseModule instance."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        reference_module = GeneralReferenceModule.from_required_elements()

        result = GeneralReferenceValidator.validate_required_elements(reference_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No required elements in this module
        assert result.is_valid

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with Dataset instance."""
        dataset = Dataset()

        # Test with valid conditional requirements
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "REORIENTED_ONLY"
        source_item.PatientOrientation = ["L", "P"]
        dataset.SourceImageSequence = [source_item]

        result = GeneralReferenceValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        if result.has_errors:
            print(f"Unexpected errors: {result.errors}")
        assert not result.has_errors

        # Test with missing conditional requirement
        source_item_invalid = Dataset()
        source_item_invalid.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item_invalid.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item_invalid.SpatialLocationsPreserved = "REORIENTED_ONLY"
        # Missing PatientOrientation
        dataset_invalid = Dataset()
        dataset_invalid.SourceImageSequence = [source_item_invalid]

        result_invalid = GeneralReferenceValidator.validate_conditional_requirements(dataset_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_errors
        assert "Patient Orientation" in str(result_invalid.errors)
        assert "Type 1C" in str(result_invalid.errors)

    def test_validate_conditional_requirements_with_base_module(self):
        """Test validate_conditional_requirements method with BaseModule instance."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        # Test with valid conditional requirements
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            spatial_locations_preserved="REORIENTED_ONLY",
            patient_orientation=["L", "P"]
        )
        reference_module = GeneralReferenceModule.from_required_elements()
        reference_module.with_optional_elements(source_image_sequence=[source_item])

        result = GeneralReferenceValidator.validate_conditional_requirements(reference_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with missing conditional requirement
        source_item_invalid = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            spatial_locations_preserved="REORIENTED_ONLY"
            # Missing patient_orientation
        )
        reference_module_invalid = GeneralReferenceModule.from_required_elements()
        reference_module_invalid.with_optional_elements(source_image_sequence=[source_item_invalid])

        result_invalid = GeneralReferenceValidator.validate_conditional_requirements(reference_module_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_errors
        assert "Patient Orientation" in str(result_invalid.errors)
        assert "Type 1C" in str(result_invalid.errors)

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with Dataset instance."""
        dataset = Dataset()

        # Test with valid enumerated values
        source_item = Dataset()
        source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item.SpatialLocationsPreserved = "YES"
        dataset.SourceImageSequence = [source_item]

        result = GeneralReferenceValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_warnings

        # Test with invalid enumerated value
        source_item_invalid = Dataset()
        source_item_invalid.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        source_item_invalid.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        source_item_invalid.SpatialLocationsPreserved = "INVALID_VALUE"
        dataset_invalid = Dataset()
        dataset_invalid.SourceImageSequence = [source_item_invalid]

        result_invalid = GeneralReferenceValidator.validate_enumerated_values(dataset_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_warnings
        assert "should be one of" in str(result_invalid.warnings)

    def test_validate_enumerated_values_with_base_module(self):
        """Test validate_enumerated_values method with BaseModule instance."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        # Test with valid enumerated values
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            spatial_locations_preserved="NO"
        )
        reference_module = GeneralReferenceModule.from_required_elements()
        reference_module.with_optional_elements(source_image_sequence=[source_item])

        result = GeneralReferenceValidator.validate_enumerated_values(reference_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_warnings

        # Test with invalid enumerated value
        source_item_invalid = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            spatial_locations_preserved="INVALID_VALUE"
        )
        reference_module_invalid = GeneralReferenceModule.from_required_elements()
        reference_module_invalid.with_optional_elements(source_image_sequence=[source_item_invalid])

        result_invalid = GeneralReferenceValidator.validate_enumerated_values(reference_module_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_warnings
        assert "should be one of" in str(result_invalid.warnings)

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with Dataset instance."""
        dataset = Dataset()

        # Test with valid sequence structure
        ref_instance_item = Dataset()
        ref_instance_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"
        ref_instance_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"

        purpose_item = Dataset()
        purpose_item.CodeValue = "121322"
        purpose_item.CodingSchemeDesignator = "DCM"
        purpose_item.CodeMeaning = "Source for processing"
        ref_instance_item.PurposeOfReferenceCodeSequence = [purpose_item]

        dataset.ReferencedInstanceSequence = [ref_instance_item]

        result = GeneralReferenceValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid sequence structure (missing Purpose of Reference Code Sequence)
        ref_instance_item_invalid = Dataset()
        ref_instance_item_invalid.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"
        ref_instance_item_invalid.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        # Missing PurposeOfReferenceCodeSequence

        dataset_invalid = Dataset()
        dataset_invalid.ReferencedInstanceSequence = [ref_instance_item_invalid]

        result_invalid = GeneralReferenceValidator.validate_sequence_structures(dataset_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_errors
        assert "Purpose of Reference Code Sequence" in str(result_invalid.errors)
        assert "Type 1" in str(result_invalid.errors)

    def test_validate_sequence_structures_with_base_module(self):
        """Test validate_sequence_structures method with BaseModule instance."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        # Test with valid sequence structure
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source for processing"

        ref_instance_item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.3",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            purpose_of_reference_code_sequence=[purpose_code_item]
        )
        reference_module = GeneralReferenceModule.from_required_elements()
        reference_module.with_optional_elements(referenced_instance_sequence=[ref_instance_item])

        result = GeneralReferenceValidator.validate_sequence_structures(reference_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid sequence structure
        ref_instance_item_invalid = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.3",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9",
            purpose_of_reference_code_sequence=[]  # Empty - should cause error
        )
        reference_module_invalid = GeneralReferenceModule.from_required_elements()
        reference_module_invalid.with_optional_elements(referenced_instance_sequence=[ref_instance_item_invalid])

        result_invalid = GeneralReferenceValidator.validate_sequence_structures(reference_module_invalid)

        assert isinstance(result_invalid, ValidationResult)
        assert result_invalid.has_errors
        assert "Purpose of Reference Code Sequence" in str(result_invalid.errors)

    def test_main_validate_method_with_both_types(self):
        """Test main validate method with both Dataset and BaseModule instances."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        # Test with Dataset
        dataset = Dataset()
        result_dataset = GeneralReferenceValidator.validate(dataset)
        assert isinstance(result_dataset, ValidationResult)
        assert not result_dataset.has_errors
        assert result_dataset.is_valid

        # Test with BaseModule
        reference_module = GeneralReferenceModule.from_required_elements()
        result_module = GeneralReferenceValidator.validate(reference_module)
        assert isinstance(result_module, ValidationResult)
        assert not result_module.has_errors
        assert result_module.is_valid

    def test_granular_methods_use_in_operator(self):
        """Test that granular methods use 'in' operator instead of hasattr/getattr."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        # Create module with minimal data to test attribute access patterns
        reference_module = GeneralReferenceModule()

        # Test that validation works with 'in' operator pattern
        result = GeneralReferenceValidator.validate_required_elements(reference_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No required elements in this module

        # Verify we can use 'in' operator directly
        assert 'DerivationDescription' not in reference_module
        assert 'SourceImageSequence' not in reference_module

    def test_zero_copy_performance_validation(self):
        """Test that BaseModule validation doesn't create Dataset copies."""
        from src.pyrt_dicom.modules.general_reference_module import GeneralReferenceModule

        reference_module = GeneralReferenceModule.from_required_elements()

        # Test all granular methods with BaseModule (should use zero-copy)
        methods_to_test = [
            GeneralReferenceValidator.validate_required_elements,
            GeneralReferenceValidator.validate_conditional_requirements,
            GeneralReferenceValidator.validate_enumerated_values,
            GeneralReferenceValidator.validate_sequence_structures,
        ]

        for method in methods_to_test:
            result = method(reference_module)
            assert isinstance(result, ValidationResult)
            # Validation should work directly on module without creating Dataset copy

        # Test main validate method with BaseModule
        result = GeneralReferenceValidator.validate(reference_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
