"""
Test ImagePlaneValidator functionality.

Tests comprehensive validation logic for DICOM Image Plane Module (PS3.3 C.7.6.2)
including Type 1/2/3 element validation, paired requirements, geometric constraints,
and cross-field relationships using the composition-based architecture.
"""

import pydicom
from pydicom.multival import MultiValue
from pyrt_dicom.validators.modules.image_plane_validator import ImagePlaneValidator
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.modules.image_plane_module import ImagePlaneModule


class TestImagePlaneValidator:
    """Test ImagePlaneValidator validation logic."""
    
    def create_valid_dataset(self) -> pydicom.Dataset:
        """Create a valid dataset for testing."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        return ds
    
    def create_valid_basemodule(self) -> ImagePlaneModule:
        """Create a valid BaseModule for testing zero-copy validation."""
        return ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
    
    def test_valid_dataset_passes_validation(self):
        """Test that a valid dataset passes validation without errors or warnings."""
        ds = self.create_valid_dataset()
        
        result = ImagePlaneValidator.validate(ds)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_valid_basemodule_passes_validation(self):
        """Test that a valid BaseModule passes validation without errors or warnings."""
        module = self.create_valid_basemodule()
        
        result = ImagePlaneValidator.validate(module)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_result_structure(self):
        """Test that ValidationResult has correct structure."""
        ds = self.create_valid_dataset()
        
        result = ImagePlaneValidator.validate(ds)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validation_with_config(self):
        """Test validation with ValidationConfig parameter."""
        ds = self.create_valid_dataset()
        config = ValidationConfig()
        
        result = ImagePlaneValidator.validate(ds, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0


class TestImagePlaneValidatorGranularMethods:
    """Test granular validation methods with Dataset and BaseModule support."""
    
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements with Dataset."""
        # Valid dataset
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        
        result = ImagePlaneValidator.validate_required_elements(ds)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid dataset - missing elements
        ds_invalid = pydicom.Dataset()
        result = ImagePlaneValidator.validate_required_elements(ds_invalid)
        assert result.has_errors
        assert len(result.errors) >= 3  # Missing all 3 Type 1 elements
    
    def test_validate_required_elements_with_basemodule(self):
        """Test validate_required_elements with BaseModule (zero-copy)."""
        # Valid module
        module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_required_elements(module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid module - empty module
        module_invalid = ImagePlaneModule()
        result = ImagePlaneValidator.validate_required_elements(module_invalid)
        assert result.has_errors
        assert len(result.errors) >= 3  # Missing all 3 Type 1 elements
    
    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements with Dataset."""
        # Valid dataset with Type 2 element
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate_conditional_requirements(ds)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid dataset - missing SliceThickness (Type 2)
        ds_invalid = pydicom.Dataset()
        ds_invalid.PixelSpacing = [1.0, 1.0]
        ds_invalid.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds_invalid.ImagePositionPatient = [0.0, 0.0, 0.0]
        # No SliceThickness
        
        result = ImagePlaneValidator.validate_conditional_requirements(ds_invalid)
        assert result.has_errors
        assert any('SliceThickness' in error for error in result.errors)
    
    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements with BaseModule (zero-copy)."""
        # Valid module
        module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_conditional_requirements(module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid module - missing SliceThickness
        module_invalid = ImagePlaneModule()
        module_invalid.PixelSpacing = [1.0, 1.0]
        module_invalid.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        module_invalid.ImagePositionPatient = [0.0, 0.0, 0.0]
        # No SliceThickness
        
        result = ImagePlaneValidator.validate_conditional_requirements(module_invalid)
        assert result.has_errors
        assert any('SliceThickness' in error for error in result.errors)
    
    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values with Dataset."""
        # Valid dataset
        ds = pydicom.Dataset()
        ds.SpacingBetweenSlices = 2.5  # Positive value
        
        result = ImagePlaneValidator.validate_enumerated_values(ds)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid dataset - negative spacing
        ds_invalid = pydicom.Dataset()
        ds_invalid.SpacingBetweenSlices = -1.0  # Negative value
        
        result = ImagePlaneValidator.validate_enumerated_values(ds_invalid)
        assert result.has_errors
        assert any('must not be negative' in error for error in result.errors)
    
    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values with BaseModule (zero-copy)."""
        # Valid module
        module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        ).with_optional_elements(spacing_between_slices=2.5)
        
        result = ImagePlaneValidator.validate_enumerated_values(module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid module - negative spacing
        module_invalid = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        ).with_optional_elements(spacing_between_slices=-1.0)
        
        result = ImagePlaneValidator.validate_enumerated_values(module_invalid)
        assert result.has_errors
        assert any('must not be negative' in error for error in result.errors)
    
    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures with Dataset."""
        # Image Plane Module has no sequences, so this should always pass
        ds = pydicom.Dataset()
        
        result = ImagePlaneValidator.validate_sequence_structures(ds)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
    
    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures with BaseModule (zero-copy)."""
        # Image Plane Module has no sequences, so this should always pass
        module = ImagePlaneModule()
        
        result = ImagePlaneValidator.validate_sequence_structures(module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
    
    def test_validate_geometric_constraints_with_dataset(self):
        """Test validate_geometric_constraints with Dataset."""
        # Valid dataset - orthogonal unit vectors
        ds = pydicom.Dataset()
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        
        result = ImagePlaneValidator.validate_geometric_constraints(ds)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid dataset - not orthogonal
        ds_invalid = pydicom.Dataset()
        ds_invalid.ImageOrientationPatient = [1, 1, 0, 0, 1, 0]
        
        result = ImagePlaneValidator.validate_geometric_constraints(ds_invalid)
        assert result.has_errors
        assert any('orthogonal' in error for error in result.errors)
    
    def test_validate_geometric_constraints_with_basemodule(self):
        """Test validate_geometric_constraints with BaseModule (zero-copy)."""
        # Valid module - orthogonal unit vectors
        module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_geometric_constraints(module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        
        # Invalid module - not orthogonal
        module_invalid = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 1, 0, 0, 1, 0],  # Not orthogonal
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_geometric_constraints(module_invalid)
        assert result.has_errors
        assert any('orthogonal' in error for error in result.errors)
    
    def test_validate_coordinate_system_with_dataset(self):
        """Test validate_coordinate_system with Dataset."""
        # Valid dataset - right-handed system
        ds = pydicom.Dataset()
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        
        result = ImagePlaneValidator.validate_coordinate_system(ds)
        assert isinstance(result, ValidationResult)
        # Should not have right-handed errors
        right_handed_errors = [error for error in result.errors if 'right-handed' in error]
        assert len(right_handed_errors) == 0
        
        # Invalid dataset - left-handed system
        ds_invalid = pydicom.Dataset()
        ds_invalid.ImageOrientationPatient = [1, 0, 0, 0, -1, 0]
        
        result = ImagePlaneValidator.validate_coordinate_system(ds_invalid)
        assert result.has_errors
        assert any('right-handed' in error for error in result.errors)
    
    def test_validate_coordinate_system_with_basemodule(self):
        """Test validate_coordinate_system with BaseModule (zero-copy)."""
        # Valid module - right-handed system
        module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_coordinate_system(module)
        assert isinstance(result, ValidationResult)
        # Should not have right-handed errors
        right_handed_errors = [error for error in result.errors if 'right-handed' in error]
        assert len(right_handed_errors) == 0
        
        # Invalid module - left-handed system
        module_invalid = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, -1, 0],  # Left-handed
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = ImagePlaneValidator.validate_coordinate_system(module_invalid)
        assert result.has_errors
        assert any('right-handed' in error for error in result.errors)


class TestImagePlaneValidatorType1Requirements:
    """Test validation of Type 1 (required) elements."""
    
    def create_minimal_dataset(self) -> pydicom.Dataset:
        """Create minimal dataset for testing."""
        return pydicom.Dataset()
    
    def test_missing_pixel_spacing_error(self):
        """Test error generation for missing Pixel Spacing (Type 1)."""
        ds = self.create_minimal_dataset()
        # Missing PixelSpacing
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Pixel Spacing (0028,0030)" in error]
        assert len(error_messages) > 0
        assert "required (Type 1)" in error_messages[0]
        assert "0028,0030" in error_messages[0]
    
    def test_missing_image_orientation_error(self):
        """Test error generation for missing Image Orientation Patient (Type 1)."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [1.0, 1.0]
        # Missing ImageOrientationPatient
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Image Orientation (Patient) (0020,0037)" in error]
        assert len(error_messages) > 0
        assert "required (Type 1)" in error_messages[0]
        assert "0020,0037" in error_messages[0]
    
    def test_missing_image_position_error(self):
        """Test error generation for missing Image Position Patient (Type 1)."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        # Missing ImagePositionPatient
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Image Position (Patient) (0020,0032)" in error]
        assert len(error_messages) > 0
        assert "required (Type 1)" in error_messages[0]
        assert "0020,0032" in error_messages[0]
    
    def test_invalid_pixel_spacing_format(self):
        """Test validation of invalid Pixel Spacing format."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [1.0]  # Wrong number of values
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Pixel Spacing (0028,0030)" in error and "pair of values" in error]
        assert len(error_messages) > 0
    
    def test_negative_pixel_spacing_values(self):
        """Test validation of negative Pixel Spacing values."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [-1.0, 1.0]  # Negative value
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "values must be positive" in error]
        assert len(error_messages) > 0
    
    def test_invalid_image_orientation_format(self):
        """Test validation of invalid Image Orientation Patient format."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1]  # Wrong number of values
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "exactly 6 direction cosine values" in error]
        assert len(error_messages) > 0
    
    def test_invalid_image_position_format(self):
        """Test validation of invalid Image Position Patient format."""
        ds = self.create_minimal_dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0]  # Wrong number of values
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "exactly 3 coordinate values" in error]
        assert len(error_messages) > 0


class TestImagePlaneValidatorType2Requirements:
    """Test validation of Type 2 (required but may be empty) elements."""
    
    def test_missing_slice_thickness_error(self):
        """Test error generation for missing Slice Thickness (Type 2)."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        # Missing SliceThickness
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Slice Thickness (0018,0050)" in error]
        assert len(error_messages) > 0
        assert "required (Type 2)" in error_messages[0]
        assert "0018,0050" in error_messages[0]
    
    def test_empty_slice_thickness_allowed(self):
        """Test that empty Slice Thickness is allowed (Type 2)."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = ""  # Empty value is allowed for Type 2
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have errors about missing SliceThickness
        error_messages = [error for error in result.errors if "Slice Thickness (0018,0050)" in error and "required" in error]
        assert len(error_messages) == 0


class TestImagePlaneValidatorPairedRequirements:
    """Test validation of paired requirements."""
    
    def test_paired_requirement_both_present_passes(self):
        """Test that having both Image Position and Orientation passes validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have paired requirement errors
        error_messages = [error for error in result.errors if "shall be provided as a pair" in error]
        assert len(error_messages) == 0
    
    def test_position_without_orientation_error(self):
        """Test error when Image Position is present but Image Orientation is missing."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        # Missing ImageOrientationPatient
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "shall be provided as a pair" in error and "Image Orientation is missing" in error]
        assert len(error_messages) > 0
    
    def test_orientation_without_position_error(self):
        """Test error when Image Orientation is present but Image Position is missing."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.SliceThickness = 2.0
        # Missing ImagePositionPatient
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "shall be provided as a pair" in error and "Image Position is missing" in error]
        assert len(error_messages) > 0


class TestImagePlaneValidatorGeometricConstraints:
    """Test validation of geometric constraints."""
    
    def test_orthogonal_vectors_pass_validation(self):
        """Test that orthogonal vectors pass geometric validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]  # Orthogonal unit vectors
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have orthogonality errors
        error_messages = [error for error in result.errors if "orthogonal" in error]
        assert len(error_messages) == 0
    
    def test_non_orthogonal_vectors_error(self):
        """Test error generation for non-orthogonal vectors."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 1, 0, 0, 1, 0]  # Not orthogonal
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "orthogonal" in error]
        assert len(error_messages) > 0
        assert "Dot product" in error_messages[0]
    
    def test_normalized_vectors_pass_validation(self):
        """Test that normalized vectors pass geometric validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]  # Unit vectors
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have normalization errors
        error_messages = [error for error in result.errors if "normalized" in error]
        assert len(error_messages) == 0
    
    def test_non_normalized_vectors_error(self):
        """Test error generation for non-normalized vectors."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [2, 0, 0, 0, 2, 0]  # Not normalized
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "normalized" in error and "unit vector" in error]
        assert len(error_messages) > 0
        assert "Magnitude" in error_messages[0]


class TestImagePlaneValidatorCoordinateSystem:
    """Test validation of coordinate system requirements."""
    
    def test_right_handed_coordinate_system_passes(self):
        """Test that right-handed coordinate system passes validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]  # Right-handed system
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have coordinate system errors
        error_messages = [error for error in result.errors if "right-handed" in error]
        assert len(error_messages) == 0
    
    def test_left_handed_coordinate_system_error(self):
        """Test error generation for left-handed coordinate system."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, -1, 0]  # Left-handed system
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "right-handed" in error]
        assert len(error_messages) > 0
        assert "Cross product z-component" in error_messages[0]


class TestImagePlaneValidatorValueConstraints:
    """Test validation of value constraints and ranges."""
    
    def test_valid_slice_thickness_passes(self):
        """Test that valid slice thickness passes validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have slice thickness errors
        slice_thickness_errors = [error for error in result.errors if "Slice Thickness" in error]
        slice_thickness_warnings = [warning for warning in result.warnings if "Slice Thickness" in warning]
        assert len(slice_thickness_errors) == 0
        assert len(slice_thickness_warnings) == 0
    
    def test_negative_slice_thickness_warning(self):
        """Test warning generation for negative slice thickness."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = -2.0  # Negative thickness
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.warnings) > 0
        warning_messages = [warning for warning in result.warnings if "should be positive" in warning]
        assert len(warning_messages) > 0
    
    def test_negative_spacing_between_slices_warning(self):
        """Test warning generation for negative spacing between slices."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        ds.SpacingBetweenSlices = -1.0  # Negative spacing
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.warnings) > 0
        warning_messages = [warning for warning in result.warnings if "should not be negative" in warning]
        assert len(warning_messages) > 0
        assert "specialized IOD" in warning_messages[0]
    
    def test_valid_spacing_between_slices_passes(self):
        """Test that valid spacing between slices passes validation."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        ds.SpacingBetweenSlices = 2.5  # Valid positive spacing
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have spacing errors for positive values
        spacing_errors = [error for error in result.errors if "SpacingBetweenSlices" in error and "negative" in error]
        assert len(spacing_errors) == 0


class TestImagePlaneValidatorCrossFieldRelationships:
    """Test validation of cross-field relationships."""
    
    def test_spacing_less_than_thickness_warning(self):
        """Test warning when spacing between slices is less than slice thickness."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 3.0
        ds.SpacingBetweenSlices = 2.0  # Less than thickness
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.warnings) > 0
        warning_messages = [warning for warning in result.warnings if "less than Slice Thickness" in warning]
        assert len(warning_messages) > 0
        assert "overlapping slices" in warning_messages[0]
    
    def test_spacing_much_larger_than_thickness_warning(self):
        """Test warning when spacing between slices is much larger than slice thickness."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        ds.SpacingBetweenSlices = 10.0  # Much larger than thickness
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.warnings) > 0
        warning_messages = [warning for warning in result.warnings if "much larger than Slice Thickness" in warning]
        assert len(warning_messages) > 0
        assert "significant gaps" in warning_messages[0]
    
    def test_contiguous_slices_no_warnings(self):
        """Test that contiguous slices (spacing = thickness) generate no warnings."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        ds.SpacingBetweenSlices = 2.0  # Same as thickness (contiguous)
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have spacing relationship warnings
        spacing_warnings = [warning for warning in result.warnings if "Spacing Between Slices" in warning and "Slice Thickness" in warning]
        assert len(spacing_warnings) == 0


class TestImagePlaneValidatorErrorMessages:
    """Test quality and specificity of error messages."""
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include specific DICOM tag references."""
        ds = pydicom.Dataset()
        # Missing all required elements
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        
        # Check that errors include DICOM tag references
        tag_patterns = ["(0028,0030)", "(0020,0037)", "(0020,0032)", "(0018,0050)"]
        for tag in tag_patterns:
            tag_errors = [error for error in result.errors if tag in error]
            assert len(tag_errors) > 0, f"No error found for tag {tag}"
    
    def test_error_messages_are_actionable(self):
        """Test that error messages provide actionable guidance."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [-1.0, 1.0]  # Invalid negative value
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        negative_error = [error for error in result.errors if "must be positive" in error][0]
        
        # Error should explain what's wrong and what to do
        assert "must be positive" in negative_error
        assert "Physical distances cannot be zero or negative" in negative_error
    
    def test_error_messages_reference_dicom_standard(self):
        """Test that error messages reference relevant DICOM standard sections."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 1, 0, 0, 1, 0]  # Not orthogonal
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert len(result.errors) > 0
        orthogonal_error = [error for error in result.errors if "orthogonal" in error][0]
        
        # Error should reference DICOM standard section
        assert "DICOM PS3.3 C.7.6.2" in orthogonal_error


class TestImagePlaneValidatorMultivalueHandling:
    """Test handling of MultiValue objects from pydicom."""
    
    def test_multivalue_pixel_spacing_validation(self):
        """Test validation with MultiValue Pixel Spacing."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = MultiValue(float, [1.0, 1.0])
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should handle MultiValue correctly
        pixel_spacing_errors = [error for error in result.errors if "Pixel Spacing" in error]
        assert len(pixel_spacing_errors) == 0
    
    def test_multivalue_image_orientation_validation(self):
        """Test validation with MultiValue Image Orientation Patient."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = MultiValue(float, [1, 0, 0, 0, 1, 0])
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should handle MultiValue correctly
        orientation_errors = [error for error in result.errors if "Image Orientation" in error]
        assert len(orientation_errors) == 0


class TestImagePlaneValidatorInputParameters:
    """Test validation of input parameters that would be passed to factory methods."""
    
    def test_pixel_spacing_input_validation_format(self):
        """Test pixel spacing format validation for factory method inputs."""
        ds = pydicom.Dataset()
        # Wrong format - not a list/tuple of 2 values
        ds.PixelSpacing = [1.0]  # Only 1 value
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must be a list or tuple of 2 values" in error and "factory method" in error 
                  for error in result.errors)
    
    def test_pixel_spacing_input_validation_positive_values(self):
        """Test pixel spacing positive value validation for factory method inputs."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [-1.0, 1.0]  # Negative value
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must be positive" in error for error in result.errors)
        
        # Test zero values
        ds.PixelSpacing = [0.0, 1.0]
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must be positive" in error for error in result.errors)
    
    def test_pixel_spacing_input_validation_numeric_values(self):
        """Test pixel spacing numeric validation handles values correctly."""
        # Test with valid numeric values that should pass
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.5, 2.5]  # Valid numeric values
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have numeric validation errors for valid values
        numeric_errors = [error for error in result.errors if "must be numeric" in error]
        assert len(numeric_errors) == 0
    
    def test_orientation_position_pair_input_validation_format(self):
        """Test image orientation and position format validation for factory method inputs."""
        # Wrong orientation format
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1]  # Only 5 values
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("6 direction cosine values" in error and "factory method" in error 
                  for error in result.errors)
        
        # Wrong position format
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0]  # Only 2 values
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("3 coordinate values" in error and "factory method" in error 
                  for error in result.errors)
    
    def test_orientation_position_pair_orthogonality_validation(self):
        """Test orthogonality validation for factory method inputs."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 1, 0, 0, 1, 0]  # Not orthogonal
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must be orthogonal" in error and "factory method" in error 
                  for error in result.errors)
    
    def test_orientation_position_pair_normalization_validation(self):
        """Test normalization validation for factory method inputs."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [2, 0, 0, 0, 2, 0]  # Not normalized
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must be normalized" in error and "factory method" in error 
                  for error in result.errors)
    
    def test_spacing_between_slices_input_validation(self):
        """Test spacing between slices validation for factory method inputs."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.SliceThickness = 2.0
        ds.SpacingBetweenSlices = -1.0  # Negative value
        
        result = ImagePlaneValidator.validate(ds)
        
        assert result.has_errors
        assert any("must not be negative" in error and "factory method" in error 
                  for error in result.errors)
        
        # Test with valid positive value - should pass numeric validation
        ds.SpacingBetweenSlices = 2.5  # Valid positive value
        result = ImagePlaneValidator.validate(ds)
        
        # Should not have numeric validation errors for valid values
        numeric_errors = [error for error in result.errors if "must be numeric" in error]
        assert len(numeric_errors) == 0
    
    def test_input_validation_with_multival(self):
        """Test that validation works with pydicom MultiValue objects."""
        ds = pydicom.Dataset()
        ds.PixelSpacing = MultiValue(float, [1.0, 1.0])
        ds.ImageOrientationPatient = MultiValue(float, [1, 0, 0, 0, 1, 0])
        ds.ImagePositionPatient = MultiValue(float, [0.0, 0.0, 0.0])
        ds.SliceThickness = 2.0
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should pass validation with MultiValue objects
        assert not result.has_errors or len([e for e in result.errors if "factory method" in e]) == 0
    
    def test_input_validation_without_attributes(self):
        """Test input validation when attributes are missing."""
        ds = pydicom.Dataset()
        # Dataset with no attributes
        
        result = ImagePlaneValidator.validate(ds)
        
        # Should have Type 1 validation errors but not input validation errors
        assert result.has_errors
        input_errors = [e for e in result.errors if "factory method" in e]
        assert len(input_errors) == 0