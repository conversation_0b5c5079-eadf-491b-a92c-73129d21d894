"""Tests for General Image Module Validator - DICOM PS3.3 C.7.6.1"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_image_validator import GeneralImageValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.modules.general_image_module import GeneralImageModule


class TestGeneralImageValidator:
    """Test class for GeneralImageValidator with comprehensive Dataset and BaseModule testing."""
    
    def test_validate_valid_minimal_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]  # Provide orientation to avoid warning
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_empty_instance_number_passes(self):
        """Test that empty instance number (Type 2) passes validation."""
        dataset = Dataset()
        dataset.InstanceNumber = ""  # Type 2 can be empty
        dataset.PatientOrientation = ["A", "F"]

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_missing_instance_number_errors(self):
        """Test that missing instance number (Type 2) generates error."""
        dataset = Dataset()
        # No InstanceNumber
        dataset.PatientOrientation = ["A", "F"]

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Instance Number (0020,0013) is required (Type 2)" in result.errors[0]
        assert "Previously named 'Image Number'" in result.errors[0]
    
    def test_validate_missing_patient_orientation_errors(self):
        """Test that missing patient orientation generates error when no spatial info."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        # No PatientOrientation, ImageOrientationPatient, or ImagePositionPatient

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert len(result.warnings) == 0
        assert "Patient Orientation (0020,0020) is required (Type 2C)" in result.errors[0]
        assert "PS3.3 C.7.6.1.1.1" in result.errors[0]
    
    def test_validate_patient_orientation_not_required_with_spatial_info(self):
        """Test that patient orientation not required when spatial info present."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        dataset.ImagePositionPatient = [0, 0, 0]
        # No PatientOrientation but has spatial info
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_patient_orientation_format_single_value_warns(self):
        """Test that single-value patient orientation generates warning."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = "A"  # Should be 2 values
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "should contain two values" in result.warnings[0]
    
    def test_validate_patient_orientation_format_wrong_count_errors(self):
        """Test that wrong number of patient orientation values generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F", "R"]  # Should be exactly 2 values
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "must contain exactly 2 values" in result.errors[0]
        assert "got 3" in result.errors[0]
    
    def test_validate_content_date_without_time_errors(self):
        """Test that content date without time generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ContentDate = "20240101"
        # No ContentTime

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert len(result.warnings) == 0
        assert "Content Time (0008,0033) is required (Type 2C)" in result.errors[0]

    def test_validate_content_time_without_date_errors(self):
        """Test that content time without date generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ContentTime = "120000"
        # No ContentDate

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert len(result.warnings) == 0
        assert "Content Date (0008,0023) is required (Type 2C)" in result.errors[0]
    
    def test_validate_quality_control_image_invalid_value_errors(self):
        """Test that invalid quality control image value generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.QualityControlImage = "INVALID"
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Quality Control Image (0028,0300) has invalid value" in result.errors[0]
        assert "INVALID" in result.errors[0]
        assert "PS3.3 C.7.6.1" in result.errors[0]
    
    def test_validate_burned_in_annotation_invalid_value_errors(self):
        """Test that invalid burned in annotation value generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.BurnedInAnnotation = "MAYBE"
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Burned In Annotation (0028,0301) has invalid value" in result.errors[0]
        assert "MAYBE" in result.errors[0]
    
    def test_validate_lossy_image_compression_invalid_value_errors(self):
        """Test that invalid lossy compression value generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.LossyImageCompression = "02"  # Should be "00" or "01"
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Lossy Image Compression (0028,2110) has invalid value" in result.errors[0]
        assert "02" in result.errors[0]
        assert "Once set to '01', shall not be reset" in result.errors[0]
    
    def test_validate_image_type_not_list_errors(self):
        """Test that non-list image type generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = "ORIGINAL"  # Should be list
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Image Type (0008,0008) must be multi-valued" in result.errors[0]
    
    def test_validate_image_type_insufficient_values_errors(self):
        """Test that image type with insufficient values generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["ORIGINAL"]  # pydicom converts single-item list to string

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        # pydicom converts single-item list to string, so we get the multi-valued error
        assert "must be multi-valued" in result.errors[0]
    
    def test_validate_image_type_invalid_pixel_data_characteristics_errors(self):
        """Test that invalid pixel data characteristics generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["INVALID", "PRIMARY"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Value 1 (Pixel Data Characteristics) has invalid value 'INVALID'" in result.errors[0]
        assert "ORIGINAL (original/source data), DERIVED (derived from other images)" in result.errors[0]
    
    def test_validate_image_type_invalid_examination_characteristics_errors(self):
        """Test that invalid examination characteristics generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["ORIGINAL", "INVALID"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Value 2 (Patient Examination Characteristics) has invalid value 'INVALID'" in result.errors[0]
        assert "PRIMARY (direct result of examination), SECONDARY (created after examination)" in result.errors[0]
    
    def test_validate_icon_image_sequence_multiple_items_errors(self):
        """Test that icon image sequence with multiple items generates error."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        
        icon1 = Dataset()
        icon1.Rows = 64
        icon1.Columns = 64
        
        icon2 = Dataset()
        icon2.Rows = 32
        icon2.Columns = 32
        
        dataset.IconImageSequence = [icon1, icon2]  # Should be single item
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "may contain only a single Item" in result.errors[0]
    
    def test_validate_lossy_compression_consistency_warns(self):
        """Test lossy compression consistency validation."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.LossyImageCompression = "01"
        # Missing ratio and method
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 2  # Should warn about missing ratio and method
        assert any("Lossy Image Compression Ratio" in w for w in result.warnings)
        assert any("Lossy Image Compression Method" in w for w in result.warnings)
    
    def test_validate_real_world_value_mapping_sequence_empty_item_warns(self):
        """Test that empty real world value mapping sequence item generates warning."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]

        empty_item = Dataset()  # No mapping attributes
        dataset.RealWorldValueMappingSequence = [empty_item]

        result = GeneralImageValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "appears to be empty" in result.warnings[0]
    
    def test_validate_with_validation_config_disabled(self):
        """Test validation with disabled configuration options."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.QualityControlImage = "INVALID"  # Would normally error
        
        config = ValidationConfig(
            check_enumerated_values=False,
            validate_conditional_requirements=False,
            validate_sequences=False
        )
        
        result = GeneralImageValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Errors disabled
        assert len(result.warnings) == 0  # Warnings disabled
    
    def test_validate_complete_valid_dataset_passes(self):
        """Test that dataset with all valid elements passes validation."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ContentDate = "20240101"
        dataset.ContentTime = "120000"
        dataset.ImageType = ["ORIGINAL", "PRIMARY"]
        dataset.QualityControlImage = "NO"
        dataset.BurnedInAnnotation = "NO"
        dataset.LossyImageCompression = "00"
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_instance_number_format_validation_warns(self):
        """Test that non-numeric instance number generates warning for VR compliance."""
        from pydicom.tag import Tag
        from pydicom.dataelem import DataElement
        
        dataset = Dataset()
        # Create a non-numeric instance number using raw DataElement to avoid pydicom validation
        try:
            # Use a direct DataElement to bypass validation during creation
            data_element = DataElement(Tag(0x0020, 0x0013), 'IS', 'ABC123')
            dataset[Tag(0x0020, 0x0013)] = data_element
            dataset.PatientOrientation = ["A", "F"]
            
            result = GeneralImageValidator.validate(dataset)
            
            assert isinstance(result, ValidationResult)
            assert len(result.errors) == 0
            assert len(result.warnings) == 1
            assert "should contain only digits for VR IS compliance" in result.warnings[0]
            assert "ABC123" in result.warnings[0]
            assert "strict DICOM compliance" in result.warnings[0]
        except Exception:
            # If pydicom prevents creating invalid IS values, skip this test
            # The test validates our validator's ability to handle non-numeric instance numbers
            # but pydicom's strict validation may prevent creating such datasets
            pytest.skip("pydicom prevents creation of invalid IS values")
    
    def test_instance_number_numeric_string_passes(self):
        """Test that numeric string instance number passes without warnings."""
        dataset = Dataset()
        dataset.InstanceNumber = "123456"  # Numeric string
        dataset.PatientOrientation = ["A", "F"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_derived_image_type_generates_warning(self):
        """Test that derived image type generates informational warning."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["DERIVED", "SECONDARY"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Image Type indicates DERIVED image" in result.warnings[0]
        assert "SOP Instance UID different" in result.warnings[0]
        assert "DICOM PS3.3 C.7.6.1.1.2" in result.warnings[0]
    
    def test_original_image_type_no_warning(self):
        """Test that original image type does not generate warnings."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["ORIGINAL", "PRIMARY"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_enhanced_error_messages_include_guidance(self):
        """Test that error messages include actionable guidance."""
        dataset = Dataset()
        # Missing required Instance Number
        dataset.PatientOrientation = ["A", "F"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        error_message = result.errors[0]
        
        # Check for enhanced guidance
        assert "To fix: Add data.InstanceNumber" in error_message
        assert "identifies this image within its Series" in error_message
        assert "Previously named 'Image Number'" in error_message
    
    def test_enhanced_lossy_compression_warnings(self):
        """Test enhanced lossy compression warning messages."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.LossyImageCompression = "01"
        # Missing ratio and method - should generate enhanced warnings
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 2
        
        # Check for enhanced warning messages
        ratio_warning = next((w for w in result.warnings if "Compression Ratio" in w), None)
        method_warning = next((w for w in result.warnings if "Compression Method" in w), None)
        
        assert ratio_warning is not None
        assert method_warning is not None
        
        # Check for actionable guidance
        assert "To fix: Add data.LossyImageCompressionRatio" in ratio_warning
        assert "To fix: Add data.LossyImageCompressionMethod" in method_warning
        assert "degree of compression applied" in ratio_warning
        assert "compression algorithm used" in method_warning
    
    def test_lossy_compression_ratio_method_mismatch_enhanced_warning(self):
        """Test enhanced warning for mismatched compression ratio and method counts."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.LossyImageCompressionRatio = [2.5, 1.5]  # 2 ratios
        dataset.LossyImageCompressionMethod = ["ISO_10918_1"]  # 1 method
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        
        warning = result.warnings[0]
        assert "Found 2 ratio(s) and 1 method(s)" in warning
        assert "successive lossy compression steps" in warning
        assert "DICOM PS3.3 C.7.6.1.1.5.2" in warning
    
    def test_real_world_value_mapping_empty_item_enhanced_warning(self):
        """Test enhanced warning for empty real world value mapping items."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        
        empty_item = Dataset()  # Empty mapping item
        dataset.RealWorldValueMappingSequence = [empty_item]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        
        warning = result.warnings[0]
        assert "appears to be empty" in warning
        assert "no value conversion information" in warning
        assert "RealWorldValueFirstValueMapped" in warning
        assert "DICOM PS3.3 C.7.6.16-12b" in warning
    
    def test_comprehensive_validation_with_all_enhancements(self):
        """Test comprehensive validation covering all enhanced functionality."""
        dataset = Dataset()
        dataset.InstanceNumber = "001"  # Numeric string - should pass
        dataset.PatientOrientation = ["A", "F"]
        dataset.ContentDate = "20240315"
        dataset.ContentTime = "143022.123"
        dataset.ImageType = ["DERIVED", "SECONDARY", "DOSE"]  # Should generate derived image warning
        dataset.QualityControlImage = "NO"
        dataset.BurnedInAnnotation = "NO"
        dataset.LossyImageCompression = "01"
        dataset.LossyImageCompressionRatio = [2.5]
        dataset.LossyImageCompressionMethod = ["ISO_10918_1"]
        
        result = GeneralImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1  # Only the derived image warning
        
        # Verify the derived image warning
        assert "Image Type indicates DERIVED image" in result.warnings[0]
    
    def test_granular_validation_methods_with_dataset(self):
        """Test all granular validation methods with Dataset."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ImageType = ["ORIGINAL", "PRIMARY"]
        dataset.QualityControlImage = "NO"
        
        # Test validate_required_elements
        result = GeneralImageValidator.validate_required_elements(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No Type 1 elements in General Image Module
        
        # Test validate_type2_elements
        result = GeneralImageValidator.validate_type2_elements(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Has InstanceNumber
        
        # Test validate_conditional_requirements
        result = GeneralImageValidator.validate_conditional_requirements(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Has PatientOrientation
        
        # Test validate_enumerated_values
        result = GeneralImageValidator.validate_enumerated_values(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # All valid enum values
        
        # Test validate_sequence_structures
        result = GeneralImageValidator.validate_sequence_structures(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No sequences present
        
        # Test validate_cross_field_consistency
        result = GeneralImageValidator.validate_cross_field_consistency(dataset)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No cross-field issues
    
    def test_granular_validation_methods_with_basemodule(self):
        """Test all granular validation methods with BaseModule (zero-copy)."""
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"],
            quality_control_image="NO"
        )
        
        # Test validate_required_elements
        result = GeneralImageValidator.validate_required_elements(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No Type 1 elements in General Image Module
        
        # Test validate_type2_elements
        result = GeneralImageValidator.validate_type2_elements(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Has InstanceNumber
        
        # Test validate_conditional_requirements
        result = GeneralImageValidator.validate_conditional_requirements(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Has PatientOrientation
        
        # Test validate_enumerated_values
        result = GeneralImageValidator.validate_enumerated_values(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # All valid enum values
        
        # Test validate_sequence_structures
        result = GeneralImageValidator.validate_sequence_structures(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No sequences present
        
        # Test validate_cross_field_consistency
        result = GeneralImageValidator.validate_cross_field_consistency(module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No cross-field issues
    
    def test_type2_elements_validation_failure_dataset(self):
        """Test Type 2 elements validation failure with Dataset."""
        dataset = Dataset()
        # Missing InstanceNumber
        dataset.PatientOrientation = ["A", "F"]
        
        result = GeneralImageValidator.validate_type2_elements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Instance Number (0020,0013) is required (Type 2)" in error for error in result.errors)
    
    def test_type2_elements_validation_failure_basemodule(self):
        """Test Type 2 elements validation failure with BaseModule."""
        # Create module without instance number by accessing internal dataset
        module = GeneralImageModule.from_required_elements(instance_number="1")
        # Remove the instance number to test validation failure
        del module._dataset.InstanceNumber
        module.with_patient_orientation(patient_orientation="A\\F")
        
        result = GeneralImageValidator.validate_type2_elements(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Instance Number (0020,0013) is required (Type 2)" in error for error in result.errors)
    
    def test_conditional_requirements_validation_failure_dataset(self):
        """Test conditional requirements validation failure with Dataset."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        # Missing PatientOrientation and no spatial orientation info
        
        result = GeneralImageValidator.validate_conditional_requirements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Patient Orientation (0020,0020) is required (Type 2C)" in error for error in result.errors)
    
    def test_conditional_requirements_validation_failure_basemodule(self):
        """Test conditional requirements validation failure with BaseModule."""
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )  # No patient orientation provided
        
        result = GeneralImageValidator.validate_conditional_requirements(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Patient Orientation (0020,0020) is required (Type 2C)" in error for error in result.errors)
    
    def test_enumerated_values_validation_failure_dataset(self):
        """Test enumerated values validation failure with Dataset."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.QualityControlImage = "INVALID_VALUE"
        
        result = GeneralImageValidator.validate_enumerated_values(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Quality Control Image (0028,0300) has invalid value" in error for error in result.errors)
    
    def test_enumerated_values_validation_failure_basemodule(self):
        """Test enumerated values validation failure with BaseModule."""
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            quality_control_image="INVALID_VALUE"
        )
        
        result = GeneralImageValidator.validate_enumerated_values(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Quality Control Image (0028,0300) has invalid value" in error for error in result.errors)
    
    def test_sequence_structures_validation_failure_dataset(self):
        """Test sequence structures validation failure with Dataset."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        
        # Create invalid icon image sequence with multiple items
        icon1 = Dataset()
        icon1.Rows = 64
        icon1.Columns = 64
        icon1.SamplesPerPixel = 1
        icon1.PhotometricInterpretation = "MONOCHROME2"
        icon1.BitsAllocated = 8
        icon1.BitsStored = 8
        icon1.HighBit = 7
        icon1.PixelRepresentation = 0
        
        icon2 = Dataset()
        icon2.Rows = 32
        icon2.Columns = 32
        
        dataset.IconImageSequence = [icon1, icon2]  # Should have only one item
        
        result = GeneralImageValidator.validate_sequence_structures(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("may contain only a single Item" in error for error in result.errors)
    
    def test_sequence_structures_validation_failure_basemodule(self):
        """Test sequence structures validation failure with BaseModule."""
        icon_item1 = GeneralImageModule.create_icon_image_item(
            rows=64, columns=64,
            photometric_interpretation="MONOCHROME2"
        )
        icon_item2 = GeneralImageModule.create_icon_image_item(
            rows=32, columns=32,
            photometric_interpretation="MONOCHROME1"
        )
        
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            icon_image_sequence=[icon_item1, icon_item2]  # Should have only one item
        )
        
        result = GeneralImageValidator.validate_sequence_structures(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("may contain only a single Item" in error for error in result.errors)
    
    def test_cross_field_consistency_validation_warnings_dataset(self):
        """Test cross-field consistency validation warnings with Dataset."""
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.LossyImageCompression = "01"  # Indicates lossy compression
        # Missing ratio and method - should generate warnings
        
        result = GeneralImageValidator.validate_cross_field_consistency(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warnings don't make it invalid
        assert result.has_warnings
        assert any("Compression Ratio" in warning for warning in result.warnings)
        assert any("Compression Method" in warning for warning in result.warnings)
    
    def test_cross_field_consistency_validation_warnings_basemodule(self):
        """Test cross-field consistency validation warnings with BaseModule."""
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            lossy_image_compression="01"  # Indicates lossy compression
            # Missing ratio and method - should generate warnings
        )
        
        result = GeneralImageValidator.validate_cross_field_consistency(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warnings don't make it invalid
        assert result.has_warnings
        assert any("Compression Ratio" in warning for warning in result.warnings)
        assert any("Compression Method" in warning for warning in result.warnings)
    
    def test_temporal_requirements_validation_dataset(self):
        """Test temporal requirements validation with Dataset."""
        # Test missing content time when content date is present
        dataset = Dataset()
        dataset.InstanceNumber = "1"
        dataset.PatientOrientation = ["A", "F"]
        dataset.ContentDate = "20240101"
        # No ContentTime
        
        result = GeneralImageValidator.validate_conditional_requirements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Content Time (0008,0033) is required (Type 2C)" in error for error in result.errors)
        
        # Test missing content date when content time is present
        dataset2 = Dataset()
        dataset2.InstanceNumber = "1"
        dataset2.PatientOrientation = ["A", "F"]
        dataset2.ContentTime = "120000"
        # No ContentDate
        
        result2 = GeneralImageValidator.validate_conditional_requirements(dataset2)
        
        assert isinstance(result2, ValidationResult)
        assert not result2.is_valid
        assert result2.has_errors
        assert any("Content Date (0008,0023) is required (Type 2C)" in error for error in result2.errors)
    
    def test_temporal_requirements_validation_basemodule(self):
        """Test temporal requirements validation with BaseModule."""
        # Test missing content time when content date is present
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_temporal_elements(
            content_date="20240101"
            # No content_time
        )
        
        result = GeneralImageValidator.validate_conditional_requirements(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert any("Content Time (0008,0033) is required (Type 2C)" in error for error in result.errors)
    
    def test_validator_independence_external_dataset(self):
        """Test validator independence with external Dataset validation."""
        # Create external dataset (not from GeneralImageModule)
        external_dataset = Dataset()
        external_dataset.InstanceNumber = "999"
        external_dataset.PatientOrientation = ["L", "H"]
        external_dataset.ContentDate = "20240315"
        external_dataset.ContentTime = "143022"
        external_dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]
        external_dataset.QualityControlImage = "NO"
        
        # Validator should work on external dataset
        result = GeneralImageValidator.validate(external_dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0
        
        # Test granular methods on external dataset
        result = GeneralImageValidator.validate_type2_elements(external_dataset)
        assert result.is_valid
        
        result = GeneralImageValidator.validate_conditional_requirements(external_dataset)
        assert result.is_valid
        
        result = GeneralImageValidator.validate_enumerated_values(external_dataset)
        assert result.is_valid
    
    def test_zero_copy_validation_basemodule_vs_dataset(self):
        """Test zero-copy validation comparing BaseModule vs Dataset validation."""
        # Create module
        module = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"]
        )
        
        # Validate with module (zero-copy)
        result_module = GeneralImageValidator.validate(module)
        
        # Validate with dataset (copy)
        dataset = module.to_dataset()
        result_dataset = GeneralImageValidator.validate(dataset)
        
        # Results should be equivalent
        assert result_module.is_valid == result_dataset.is_valid
        assert len(result_module.errors) == len(result_dataset.errors)
        assert len(result_module.warnings) == len(result_dataset.warnings)
        
        # But the input objects should be different
        assert module is not dataset
        assert type(module).__name__ == "GeneralImageModule"
        assert type(dataset).__name__ == "Dataset"
