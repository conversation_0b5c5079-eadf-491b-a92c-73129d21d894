"""
Test GeneralImageModule (C - Conditional) functionality.

GeneralImageModule implements DICOM PS3.3 C.7.6.1 General Image Module.
Required when dose data is presented as image-based grid format.
"""

import pytest
from pyrt_dicom.modules import GeneralImageModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralImageModule:
    """Test GeneralImageModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        assert dataset.InstanceNumber == "1"
        assert not hasattr(dataset, 'PatientOrientation')  # Now added via conditional method
    
    def test_instance_number_validation(self):
        """Test instance number validation."""
        # Valid integer strings only (per DICOM VR IS)
        valid_instance_numbers = ["1", "001", "123", "999"]

        for number in valid_instance_numbers:
            image = GeneralImageModule.from_required_elements(
                instance_number=number
            )
            dataset = image.to_dataset()
            assert dataset.InstanceNumber == number

        # Setting an invalid instance number causes pydicom to raise a ValueError
        with pytest.raises(ValueError):
            image = GeneralImageModule.from_required_elements(
                instance_number="123a"  # Invalid IS value
            )

            # Warning: module validation will not catch the instance number error because
            # of the ValueError raised above. However, it will catch missing Patient Orientation
            result = image.validate()
            assert not result.is_valid  # Should fail due to missing Patient Orientation
            assert result.has_errors

        # Basic creation should succeed with valid VR values and Patient Orientation
        image = GeneralImageModule.from_required_elements(
            instance_number="123"  # Valid IS value
        ).with_patient_orientation("A\\F")  # Add required Patient Orientation
        result = image.validate()
        assert result.is_valid
        assert not result.has_errors
    
    def test_patient_orientation_values(self):
        """Test various patient orientation values using conditional method."""
        # Test cases with expected pydicom parsing behavior
        test_cases = [
            ("", ""),                           # Empty for dose grids
            ("A\\F", ["A", "F"]),              # Anterior-Foot (parsed as list)
            ("P\\H", ["P", "H"]),              # Posterior-Head
            ("R\\F", ["R", "F"]),              # Right-Foot
            ("L\\H", ["L", "H"]),              # Left-Head
        ]

        for input_orientation, expected_result in test_cases:
            image = GeneralImageModule.from_required_elements(
                instance_number="1"
            ).with_patient_orientation(
                patient_orientation=input_orientation
            )
            dataset = image.to_dataset()
            assert dataset.PatientOrientation == expected_result
    
    def test_with_optional_elements(self):
        """Test adding optional image elements."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"],
            image_comments="Test dose image",
            quality_control_image="NO",
            burned_in_annotation="NO"
        )

        dataset = image.to_dataset()
        assert hasattr(dataset, 'ImageType')
        assert dataset.ImageType == ["DERIVED", "SECONDARY", "DOSE"]
        assert hasattr(dataset, 'ImageComments')
        assert hasattr(dataset, 'QualityControlImage')
        assert hasattr(dataset, 'BurnedInAnnotation')
    
    def test_image_type_for_rt_dose(self):
        """Test image type values specific to RT dose."""
        rt_dose_image_types = [
            ["DERIVED", "SECONDARY", "DOSE"],
            ["DERIVED", "SECONDARY", "DOSE", "PLAN"],
            ["DERIVED", "SECONDARY", "DOSE", "BEAM"],
            ["DERIVED", "SECONDARY", "DOSE", "FRACTION"]
        ]
        
        for image_type in rt_dose_image_types:
            image = GeneralImageModule.from_required_elements(
                instance_number="1"
            ).with_optional_elements(
                image_type=image_type
            )
            dataset = image.to_dataset()
            assert dataset.ImageType == image_type
    
    def test_content_datetime_validation(self):
        """Test content date and time validation."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_temporal_elements(
            content_date="20240101",
            content_time="120000.123"
        )

        dataset = image.to_dataset()
        assert dataset.ContentDate == "20240101"
        assert dataset.ContentTime == "120000.123"
    
    def test_acquisition_elements(self):
        """Test that acquisition elements are not part of GeneralImageModule."""
        # Note: Acquisition elements are part of GeneralAcquisitionModule, not GeneralImageModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # These elements are not supported by GeneralImageModule
        assert not hasattr(dataset, 'AcquisitionNumber')
        assert not hasattr(dataset, 'AcquisitionDate')
        assert not hasattr(dataset, 'AcquisitionTime')
    
    def test_referenced_image_sequence(self):
        """Test that referenced image sequence is not part of GeneralImageModule."""
        # Note: Referenced Image Sequence is part of other modules (e.g., GeneralReferenceModule)
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # This element is not supported by GeneralImageModule
        assert not hasattr(dataset, 'ReferencedImageSequence')

    def test_derivation_description(self):
        """Test that derivation description is not part of GeneralImageModule."""
        # Note: Derivation Description is part of ImageDerivationModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # This element is not supported by GeneralImageModule
        assert not hasattr(dataset, 'DerivationDescription')

    def test_source_image_sequence(self):
        """Test that source image sequence is not part of GeneralImageModule."""
        # Note: Source Image Sequence is part of ImageDerivationModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # This element is not supported by GeneralImageModule
        assert not hasattr(dataset, 'SourceImageSequence')

    def test_rt_dose_conditional_requirements(self):
        """Test conditional requirements for RT dose images."""
        # When presenting dose as image grid, GeneralImageModule is required
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation=""  # Often empty for dose grids
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"]
        )

        # Verify dose-specific requirements
        dataset = image.to_dataset()
        assert dataset.InstanceNumber == "1"
        assert "DOSE" in dataset.ImageType

    def test_image_orientation_patient(self):
        """Test that image orientation patient is not part of GeneralImageModule."""
        # Note: Image Orientation Patient is part of ImagePlaneModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # This element is not supported by GeneralImageModule
        assert not hasattr(dataset, 'ImageOrientationPatient')

    def test_image_position_patient(self):
        """Test that image position patient is not part of GeneralImageModule."""
        # Note: Image Position Patient is part of ImagePlaneModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )

        dataset = image.to_dataset()
        # This element is not supported by GeneralImageModule
        assert not hasattr(dataset, 'ImagePositionPatient')

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        assert hasattr(image, 'validate')
        assert callable(image.validate)
        
        # Test validation result structure
        validation_result = image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_conditional_usage_scenarios(self):
        """Test scenarios where GeneralImageModule is conditionally required."""
        # Scenario 1: Grid-based dose presentation requires GeneralImageModule
        grid_dose_image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation=""
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"]
        )

        # Scenario 2: DVH-only dose (no image grid) would not require this module
        # This would be tested in IOD-level tests

        dataset = grid_dose_image.to_dataset()
        assert dataset.ImageType == ["DERIVED", "SECONDARY", "DOSE"]

    def test_empty_patient_orientation_allowed(self):
        """Test that empty patient orientation is allowed for dose grids."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation=""  # Empty but not None (Type 2C)
        )

        dataset = image.to_dataset()
        assert dataset.PatientOrientation == ""

    def test_with_patient_orientation_method(self):
        """Test the new with_patient_orientation conditional method."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        )

        dataset = image.to_dataset()
        assert hasattr(dataset, 'PatientOrientation')
        assert dataset.PatientOrientation == ["A", "F"]  # pydicom parses as list
        assert image.has_patient_orientation

        # Test empty orientation
        image_empty = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation=""
        )

        dataset_empty = image_empty.to_dataset()
        assert hasattr(dataset_empty, 'PatientOrientation')
        assert dataset_empty.PatientOrientation == ""
        assert not image_empty.has_patient_orientation

    def test_create_real_world_value_mapping_item(self):
        """Test creation of Real World Value Mapping Sequence items."""
        item = GeneralImageModule.create_real_world_value_mapping_item(
            real_world_value_first_value_mapped=0.0,
            real_world_value_last_value_mapped=100.0,
            real_world_value_lut_data=[0.0, 50.0, 100.0]
        )

        assert hasattr(item, 'RealWorldValueFirstValueMapped')
        assert hasattr(item, 'RealWorldValueLastValueMapped')
        assert hasattr(item, 'RealWorldValueLUTData')
        assert item.RealWorldValueFirstValueMapped == 0.0
        assert item.RealWorldValueLastValueMapped == 100.0
        assert item.RealWorldValueLUTData == [0.0, 50.0, 100.0]

    def test_new_properties(self):
        """Test new logical properties."""
        # Test has_patient_orientation
        image = GeneralImageModule.from_required_elements(instance_number="1")
        assert not image.has_patient_orientation

        image.with_patient_orientation("A\\F")
        assert image.has_patient_orientation

        # Test has_icon_image
        assert not image.has_icon_image

        icon_item = GeneralImageModule.create_icon_image_item(
            rows=64, columns=64, pixel_data=b"test_data"
        )
        image.with_optional_elements(icon_image_sequence=[icon_item])
        assert image.has_icon_image

    def test_to_dataset_method(self):
        """Test the to_dataset() method returns proper pydicom Dataset."""
        import pydicom

        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"],
            image_comments="Test image"
        )

        dataset = image.to_dataset()

        # Verify it's a proper pydicom Dataset
        assert isinstance(dataset, pydicom.Dataset)

        # Verify all attributes are present
        assert dataset.InstanceNumber == "1"
        assert dataset.PatientOrientation == ["A", "F"]
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY"]
        assert dataset.ImageComments == "Test image"

        # Verify it's a copy (not the same object)
        assert dataset is not image._dataset

        # Verify changes to returned dataset don't affect module
        dataset.InstanceNumber = "999"
        assert image.InstanceNumber == "1"
    
    def test_temporal_elements_consistency(self):
        """Test temporal elements storage and properties."""
        image = GeneralImageModule.from_required_elements(instance_number="1")
        
        # Test that providing both date and time works
        image.with_temporal_elements(
            content_date="20240101",
            content_time="120000"
        )
        assert image.has_temporal_info
        assert image.has_complete_temporal_info
        assert not image.has_partial_temporal_info
        
        # Test that providing only date is allowed but creates partial temporal info
        image_date_only = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_temporal_elements(
            content_date="20240101"
            # No content_time
        )
        assert not image_date_only.has_complete_temporal_info
        assert image_date_only.has_partial_temporal_info
        
        # Test that providing only time is allowed but creates partial temporal info
        image_time_only = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_temporal_elements(
            content_time="120000"
            # No content_date
        )
        assert not image_time_only.has_complete_temporal_info
        assert image_time_only.has_partial_temporal_info
    
    def test_enhanced_properties(self):
        """Test enhanced and new properties."""
        # Start with minimal image
        image = GeneralImageModule.from_required_elements(instance_number="1")
        
        # Test image type summary property
        assert image.image_type_summary == "Not specified"
        
        # Add image type and test summary
        image.with_optional_elements(image_type=["ORIGINAL", "PRIMARY"])
        assert image.image_type_summary == "ORIGINAL PRIMARY"
        
        # Test single value image type
        image2 = GeneralImageModule.from_required_elements(instance_number="2")
        # Simulate single value (though pydicom usually converts to list)
        image2['ImageType'] = "DERIVED"
        assert image2.image_type_summary == "DERIVED"
        
        # Test temporal relationship properties
        assert not image.is_temporally_related
        assert not image.has_temporal_info
        assert not image.has_complete_temporal_info
        assert not image.has_partial_temporal_info
        
        # Add temporal elements
        image.with_temporal_elements(
            content_date="20240101",
            content_time="120000"
        )
        assert image.is_temporally_related
        assert image.has_temporal_info
        assert image.has_complete_temporal_info
        assert not image.has_partial_temporal_info
        
        # Test real world value mapping property
        assert not image.has_real_world_value_mapping
        
        mapping_item = GeneralImageModule.create_real_world_value_mapping_item(
            real_world_value_first_value_mapped=0.0,
            real_world_value_last_value_mapped=100.0
        )
        image.with_optional_elements(real_world_value_mapping_sequence=[mapping_item])
        assert image.has_real_world_value_mapping
    
    def test_comprehensive_validation_integration(self):
        """Test comprehensive validation with various configurations."""
        # Valid complete image
        complete_image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_temporal_elements(
            content_date="20240315",
            content_time="143022.123"
        ).with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"],
            quality_control_image="NO",
            burned_in_annotation="NO",
            recognizable_visual_features="YES"
        )
        
        result = complete_image.validate()
        assert result.is_valid
        assert len(result.errors) == 0
        
        # Test that validation method exists and returns proper structure
        assert hasattr(complete_image, 'validate')
        assert callable(complete_image.validate)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_granular_validation_methods(self):
        """Test all granular validation methods."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"],
            quality_control_image="NO"
        )
        
        # Test all public validation methods exist and are callable
        assert hasattr(image, 'check_required_elements')
        assert callable(image.check_required_elements)
        assert hasattr(image, 'check_type2_elements')
        assert callable(image.check_type2_elements)
        assert hasattr(image, 'check_conditional_requirements')
        assert callable(image.check_conditional_requirements)
        assert hasattr(image, 'check_enum_constraints')
        assert callable(image.check_enum_constraints)
        assert hasattr(image, 'check_sequence_requirements')
        assert callable(image.check_sequence_requirements)
        assert hasattr(image, 'check_cross_field_consistency')
        assert callable(image.check_cross_field_consistency)
        
        # Test each method returns ValidationResult
        result = image.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        
        result = image.check_type2_elements()
        assert isinstance(result, ValidationResult)
        
        result = image.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        
        result = image.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        
        result = image.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        
        result = image.check_cross_field_consistency()
        assert isinstance(result, ValidationResult)
    
    def test_private_validation_methods_exist(self):
        """Test private validation methods exist but are not part of public API."""
        image = GeneralImageModule.from_required_elements(instance_number="1")
        
        # Test private methods exist
        assert hasattr(image, '_ensure_required_elements_valid')
        assert hasattr(image, '_ensure_type2_elements_valid')
        assert hasattr(image, '_ensure_conditional_requirements_valid')
        assert hasattr(image, '_ensure_enum_constraints_valid')
        assert hasattr(image, '_ensure_sequence_requirements_valid')
        assert hasattr(image, '_ensure_cross_field_consistency_valid')
        
        # Test private methods are callable
        assert callable(image._ensure_required_elements_valid)
        assert callable(image._ensure_type2_elements_valid)
        assert callable(image._ensure_conditional_requirements_valid)
        assert callable(image._ensure_enum_constraints_valid)
        assert callable(image._ensure_sequence_requirements_valid)
        assert callable(image._ensure_cross_field_consistency_valid)
    
    def test_private_validation_exception_handling(self):
        """Test private validation methods raise ValidationError on failure."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        # Test with missing patient orientation (conditional requirement)
        image = GeneralImageModule.from_required_elements(instance_number="1")
        # No spatial orientation info, so PatientOrientation should be required
        
        # Should pass for methods that don't have validation issues
        try:
            image._ensure_required_elements_valid()  # Should pass - no Type 1 elements
        except ValidationError:
            assert False, "Required elements validation should pass"
        
        try:
            image._ensure_type2_elements_valid()  # Should pass - has InstanceNumber
        except ValidationError:
            assert False, "Type 2 elements validation should pass"
        
        # Should raise exception for missing patient orientation
        try:
            image._ensure_conditional_requirements_valid()
            assert False, "Should have raised ValidationError for missing PatientOrientation"
        except ValidationError as e:
            assert "Patient Orientation" in str(e)
    
    def test_zero_copy_validation(self):
        """Test that validation uses zero-copy optimization (passes self, not dataset)."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        )
        
        # Create a mock to verify the validator is called with the module instance
        import unittest.mock
        with unittest.mock.patch(
            'pyrt_dicom.validators.modules.general_image_validator.GeneralImageValidator.validate'
        ) as mock_validate:
            mock_validate.return_value = ValidationResult()
            
            image.validate()
            
            # Verify the validator was called with the module instance (self), not the dataset
            mock_validate.assert_called_once()
            args, kwargs = mock_validate.call_args
            assert args[0] is image  # First argument should be the module instance
            assert args[0] is not image._dataset  # Should NOT be the internal dataset
    
    def test_validation_with_invalid_enumerated_values(self):
        """Test validation with invalid enumerated values."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            quality_control_image="INVALID_VALUE"  # Invalid enum value
        )
        
        # Test granular enum validation
        result = image.check_enum_constraints()
        assert not result.is_valid
        assert result.has_errors
        assert any("Quality Control Image" in error for error in result.errors)
        
        # Test comprehensive validation
        result = image.validate()
        assert not result.is_valid
        assert result.has_errors
    
    def test_validation_with_missing_conditional_requirements(self):
        """Test validation with missing Type 2C conditional requirements."""
        # Image without patient orientation and no spatial orientation info
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        )  # No patient orientation provided
        
        # Test granular conditional requirements validation
        result = image.check_conditional_requirements()
        assert not result.is_valid
        assert result.has_errors
        assert any("Patient Orientation" in error for error in result.errors)
        
        # Test comprehensive validation
        result = image.validate()
        assert not result.is_valid
        assert result.has_errors
    
    def test_validation_with_temporal_inconsistency(self):
        """Test validation with inconsistent temporal elements."""
        # Image with content date but no content time
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_temporal_elements(
            content_date="20240101"
            # No content_time - should trigger Type 2C validation error
        )
        
        # Test granular conditional requirements validation
        result = image.check_conditional_requirements()
        assert not result.is_valid
        assert result.has_errors
        assert any("Content Time" in error for error in result.errors)
        
        # Test comprehensive validation
        result = image.validate()
        assert not result.is_valid
        assert result.has_errors
    
    def test_sequence_structure_validation(self):
        """Test sequence structure validation."""
        # Test with valid icon image sequence
        icon_item = GeneralImageModule.create_icon_image_item(
            rows=64, columns=64,
            photometric_interpretation="MONOCHROME2",
            pixel_data=b"test_data"
        )
        
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            icon_image_sequence=[icon_item]
        )
        
        # Test granular sequence validation
        result = image.check_sequence_requirements()
        assert result.is_valid  # Should pass with valid single icon item
        
        # Test with multiple icon items (should fail)
        icon_item2 = GeneralImageModule.create_icon_image_item(
            rows=32, columns=32,
            photometric_interpretation="MONOCHROME1"
        )
        
        image2 = GeneralImageModule.from_required_elements(
            instance_number="2"
        ).with_patient_orientation(
            patient_orientation="P\\H"
        ).with_optional_elements(
            icon_image_sequence=[icon_item, icon_item2]  # Multiple items should fail
        )
        
        result2 = image2.check_sequence_requirements()
        assert not result2.is_valid
        assert result2.has_errors
        assert any("single Item" in error for error in result2.errors)
    
    def test_cross_field_consistency_validation(self):
        """Test cross-field consistency validation."""
        # Test lossy compression consistency
        image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            lossy_image_compression="01",  # Indicates lossy compression
            # Missing ratio and method - should generate warnings
        )
        
        # Test granular cross-field validation
        result = image.check_cross_field_consistency()
        assert result.is_valid  # Warnings don't make it invalid
        assert result.has_warnings  # Should have warnings about missing ratio/method
        assert any("Compression Ratio" in warning for warning in result.warnings)
        assert any("Compression Method" in warning for warning in result.warnings)
        
        # Test with consistent lossy compression attributes
        image2 = GeneralImageModule.from_required_elements(
            instance_number="2"
        ).with_patient_orientation(
            patient_orientation="A\\F"
        ).with_optional_elements(
            lossy_image_compression="01",
            lossy_image_compression_ratio=[2.5],
            lossy_image_compression_method=["ISO_10918_1"]
        )
        
        result2 = image2.check_cross_field_consistency()
        assert result2.is_valid
        assert not result2.has_warnings  # Should not have warnings with complete info
    
    def test_lossy_compression_method_consistency(self):
        """Test lossy compression method storage (pydicom converts single-item lists)."""
        image = GeneralImageModule.from_required_elements(instance_number="1")
        
        # Test single method - pydicom converts single-item list to single value for CS VR
        image.with_optional_elements(
            lossy_image_compression_method="ISO_10918_1"
        )
        
        dataset = image.to_dataset()
        # pydicom converts single-item list back to single value for CS VR
        assert dataset.LossyImageCompressionMethod == "ISO_10918_1"
        
        # Test multiple methods stay as list
        image2 = GeneralImageModule.from_required_elements(instance_number="2")
        image2.with_optional_elements(
            lossy_image_compression_method=["ISO_10918_1", "ISO_15444_1"]
        )
        
        dataset2 = image2.to_dataset()
        # pydicom may use MultiValue type for multi-valued CS elements
        assert hasattr(dataset2.LossyImageCompressionMethod, '__iter__')
        assert list(dataset2.LossyImageCompressionMethod) == ["ISO_10918_1", "ISO_15444_1"]