"""
Test GeneralReferenceModule functionality.

GeneralReferenceModule implements DICOM PS3.3 C.12.4 General Reference Module.
References source and other related Instances and describes the manner of derivation.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import GeneralReferenceModule
from pyrt_dicom.enums.common_enums import SpatialLocationsPreserved
from pyrt_dicom.validators import ValidationResult


class TestGeneralReferenceModule:
    """Test GeneralReferenceModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        reference = GeneralReferenceModule.from_required_elements()

        # Module should be created successfully with no attributes initially
        assert isinstance(reference, GeneralReferenceModule)
        assert not isinstance(reference, Dataset)  # No longer inherits from Dataset
        assert reference.has_data is False  # No data initially

        # Should be able to generate empty dataset
        dataset = reference.to_dataset()
        assert isinstance(dataset, Dataset)
        assert len(dataset) == 0
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create sequence using the helper method to get proper Dataset objects
        ref_image_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]

        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            derivation_description="Contrast enhanced image",
            referenced_image_sequence=ref_image_seq
        )

        # Test via to_dataset() method
        dataset = reference.to_dataset()
        assert hasattr(dataset, 'DerivationDescription')
        assert dataset.DerivationDescription == "Contrast enhanced image"
        assert hasattr(dataset, 'ReferencedImageSequence')
        assert len(dataset.ReferencedImageSequence) == 1

        # Test properties work correctly
        assert reference.has_referenced_images
        assert reference.has_derivation_info
    
    def test_create_referenced_image_item_basic(self):
        """Test basic referenced image item creation."""
        item = GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert not hasattr(item, 'ReferencedFrameNumber')
        assert not hasattr(item, 'PurposeOfReferenceCodeSequence')
    
    def test_create_referenced_image_item_with_frames(self):
        """Test referenced image item creation with frame numbers."""
        item = GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            referenced_frame_number=[1, 2, 3]
        )
        
        assert item.ReferencedFrameNumber == [1, 2, 3]
    
    def test_create_source_image_item_basic(self):
        """Test basic source image item creation."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
    
    def test_create_source_image_item_with_spatial_locations(self):
        """Test source image item creation with spatial locations preserved."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved=SpatialLocationsPreserved.YES
        )

        assert item.SpatialLocationsPreserved == "YES"

    def test_create_source_image_item_with_reoriented_only(self):
        """Test source image item creation with REORIENTED_ONLY (validation moved to validator)."""
        # Test that REORIENTED_ONLY without patient orientation creates item (validation moved to validator)
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved=SpatialLocationsPreserved.REORIENTED_ONLY
            # No patient_orientation - item creation succeeds, validation handled by validator
        )

        assert item.SpatialLocationsPreserved == "REORIENTED_ONLY"
        assert not hasattr(item, 'PatientOrientation')

        # Test that REORIENTED_ONLY with patient orientation works
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved=SpatialLocationsPreserved.REORIENTED_ONLY,
            patient_orientation=["L", "P"]
        )

        assert item.SpatialLocationsPreserved == "REORIENTED_ONLY"
        assert item.PatientOrientation == ["L", "P"]
    
    def test_create_source_image_item_with_patient_orientation(self):
        """Test source image item creation with patient orientation."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            patient_orientation=["L", "P"]
        )
        
        assert item.PatientOrientation == ["L", "P"]
    
    def test_create_referenced_instance_item(self):
        """Test referenced instance item creation."""
        # Create proper Dataset for purpose code sequence
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source image for image processing operation"
        purpose_code = [purpose_code_item]

        item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=purpose_code
        )

        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert len(item.PurposeOfReferenceCodeSequence) == 1
        assert item.PurposeOfReferenceCodeSequence[0].CodeValue == "121322"

    def test_create_referenced_instance_item_without_purpose_code(self):
        """Test referenced instance item creation without purpose code (validation moved to validator)."""
        # Test that empty or missing purpose code sequence creates item (validation moved to validator)
        item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[]
        )

        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert not hasattr(item, 'PurposeOfReferenceCodeSequence')

    def test_create_source_instance_item(self):
        """Test source instance item creation."""
        item = GeneralReferenceModule.create_source_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )

        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert not hasattr(item, 'PurposeOfReferenceCodeSequence')

        # Test with purpose code sequence
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source for processing"

        item_with_purpose = GeneralReferenceModule.create_source_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[purpose_code_item]
        )

        assert hasattr(item_with_purpose, 'PurposeOfReferenceCodeSequence')
        assert len(item_with_purpose.PurposeOfReferenceCodeSequence) == 1
    
    def test_has_referenced_images_property(self):
        """Test has_referenced_images property."""
        reference = GeneralReferenceModule.from_required_elements()

        # Initially no referenced images
        assert not reference.has_referenced_images

        # Add referenced images using helper method
        ref_image_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        reference.with_optional_elements(referenced_image_sequence=ref_image_seq)

        assert reference.has_referenced_images

    def test_has_referenced_instances_property(self):
        """Test has_referenced_instances property."""
        reference = GeneralReferenceModule.from_required_elements()

        # Initially no referenced instances
        assert not reference.has_referenced_instances

        # Add referenced instances using helper method
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source for processing"

        ref_instance_seq = [GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[purpose_code_item]
        )]
        reference.with_optional_elements(referenced_instance_sequence=ref_instance_seq)

        assert reference.has_referenced_instances

    def test_has_source_instances_property(self):
        """Test has_source_instances property."""
        reference = GeneralReferenceModule.from_required_elements()

        # Initially no source instances
        assert not reference.has_source_instances

        # Add source instances using helper method
        source_instance_seq = [GeneralReferenceModule.create_source_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        reference.with_optional_elements(source_instance_sequence=source_instance_seq)

        assert reference.has_source_instances
    
    def test_has_derivation_info_property(self):
        """Test has_derivation_info property."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Initially no derivation info
        assert not reference.has_derivation_info
        
        # Add derivation description
        reference.with_optional_elements(
            derivation_description="Test derivation"
        )
        
        assert reference.has_derivation_info
    
    def test_has_source_images_property(self):
        """Test has_source_images property."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Initially no source images
        assert not reference.has_source_images
        
        # Add source images using helper method
        source_image_seq = [GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        reference.with_optional_elements(source_image_sequence=source_image_seq)
        
        assert reference.has_source_images
    
    def test_all_optional_elements(self):
        """Test setting all optional elements."""
        # Create reference sequences using helper methods
        reference_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]

        # Create derivation code sequence with proper Dataset
        derivation_code_item = Dataset()
        derivation_code_item.CodeValue = "113076"
        derivation_code_item.CodingSchemeDesignator = "DCM"
        derivation_code_item.CodeMeaning = "Segmentation"
        derivation_code = [derivation_code_item]

        # Create instance sequence using helper method
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source image for image processing operation"
        instance_seq = [GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[purpose_code_item]
        )]

        # Create source instance sequence using helper method
        source_instance_seq = [GeneralReferenceModule.create_source_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )]

        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            referenced_image_sequence=reference_seq,
            referenced_instance_sequence=instance_seq,
            derivation_description="Image derived from source",
            derivation_code_sequence=derivation_code,
            source_image_sequence=reference_seq,
            source_instance_sequence=source_instance_seq
        )

        # Test via to_dataset() method
        dataset = reference.to_dataset()
        assert hasattr(dataset, 'ReferencedImageSequence')
        assert hasattr(dataset, 'ReferencedInstanceSequence')
        assert hasattr(dataset, 'DerivationDescription')
        assert hasattr(dataset, 'DerivationCodeSequence')
        assert hasattr(dataset, 'SourceImageSequence')
        assert hasattr(dataset, 'SourceInstanceSequence')

        # Test all properties
        assert reference.has_referenced_images
        assert reference.has_referenced_instances
        assert reference.has_derivation_info
        assert reference.has_source_images
        assert reference.has_source_instances
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        reference = GeneralReferenceModule.from_required_elements()
        
        assert hasattr(reference, 'validate')
        assert callable(reference.validate)
        
        # Test validation result structure
        validation_result = reference.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            derivation_description=None,
            referenced_image_sequence=None
        )

        # None values should not create attributes in the internal dataset
        dataset = reference.to_dataset()
        assert not hasattr(dataset, 'DerivationDescription')
        assert not hasattr(dataset, 'ReferencedImageSequence')

        # Properties should return False
        assert not reference.has_derivation_info
        assert not reference.has_referenced_images

    def test_to_dataset_method(self):
        """Test to_dataset() method returns proper DICOM dataset."""
        reference = GeneralReferenceModule.from_required_elements()

        # Empty module should return empty dataset
        dataset = reference.to_dataset()
        assert isinstance(dataset, Dataset)
        assert len(dataset) == 0

        # Add some elements and test
        reference.with_optional_elements(
            derivation_description="Test derivation"
        )

        dataset = reference.to_dataset()
        assert len(dataset) == 1
        assert dataset.DerivationDescription == "Test derivation"

        # Verify it's a copy (modifying returned dataset shouldn't affect module)
        dataset.DerivationDescription = "Modified"
        new_dataset = reference.to_dataset()
        assert new_dataset.DerivationDescription == "Test derivation"  # Should be unchanged

    def test_validate_method_zero_copy(self):
        """Test that validate method uses zero-copy optimization."""
        reference = GeneralReferenceModule.from_required_elements()

        # Test validation works with zero-copy (passes self instead of dataset)
        result = reference.validate()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # Empty module should be valid

    def test_check_required_elements_method(self):
        """Test check_required_elements public convenience method."""
        reference = GeneralReferenceModule.from_required_elements()

        # Test with empty module (all elements are Type 3)
        result = reference.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No required elements in this module
        assert result.is_valid

    def test_check_conditional_requirements_method(self):
        """Test check_conditional_requirements public convenience method."""
        reference = GeneralReferenceModule.from_required_elements()

        # Test with valid conditional requirements
        result = reference.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid conditional requirements (missing Patient Orientation when REORIENTED_ONLY)
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="REORIENTED_ONLY"
            # Missing patient_orientation - should cause error
        )
        reference.with_optional_elements(source_image_sequence=[source_item])

        result = reference.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert "Patient Orientation" in str(result.errors)
        assert "Type 1C" in str(result.errors)

    def test_check_enum_constraints_method(self):
        """Test check_enum_constraints public convenience method."""
        reference = GeneralReferenceModule.from_required_elements()

        # Test with valid enum values
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="YES"
        )
        reference.with_optional_elements(source_image_sequence=[source_item])

        result = reference.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid enum value
        source_item_invalid = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="INVALID_VALUE"
        )
        reference_invalid = GeneralReferenceModule.from_required_elements()
        reference_invalid.with_optional_elements(source_image_sequence=[source_item_invalid])

        result = reference_invalid.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert result.has_warnings  # Enum violations are warnings
        assert "should be one of" in str(result.warnings)

    def test_check_sequence_requirements_method(self):
        """Test check_sequence_requirements public convenience method."""
        reference = GeneralReferenceModule.from_required_elements()

        # Test with valid sequence structure
        ref_instance_item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[Dataset()]  # Valid purpose code
        )
        reference.with_optional_elements(referenced_instance_sequence=[ref_instance_item])

        result = reference.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid sequence structure (missing required Purpose of Reference Code Sequence)
        ref_instance_item_invalid = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[]  # Empty - should cause error
        )
        reference_invalid = GeneralReferenceModule.from_required_elements()
        reference_invalid.with_optional_elements(referenced_instance_sequence=[ref_instance_item_invalid])

        result = reference_invalid.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert "Purpose of Reference Code Sequence" in str(result.errors)
        assert "Type 1" in str(result.errors)

    def test_private_validation_methods_success(self):
        """Test private validation methods that raise ValidationError on failure - success cases."""
        from pyrt_dicom.validators.validation_error import ValidationError

        reference = GeneralReferenceModule.from_required_elements()

        # All private methods should succeed with valid data (no exceptions raised)
        try:
            reference._ensure_required_elements_valid()
            reference._ensure_conditional_requirements_valid()
            reference._ensure_enum_constraints_valid()
            reference._ensure_sequence_requirements_valid()
        except ValidationError:
            pytest.fail("Private validation methods should not raise ValidationError with valid data")

    def test_private_validation_methods_conditional_failure(self):
        """Test private validation methods that raise ValidationError - conditional requirements failure."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with invalid conditional requirements
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="REORIENTED_ONLY"
            # Missing patient_orientation - should cause error
        )
        reference = GeneralReferenceModule.from_required_elements()
        reference.with_optional_elements(source_image_sequence=[source_item])

        # Should raise ValidationError for conditional requirements
        with pytest.raises(ValidationError) as exc_info:
            reference._ensure_conditional_requirements_valid()

        assert "Conditional requirements validation failed" in str(exc_info.value)
        assert "Patient Orientation" in str(exc_info.value)

    def test_private_validation_methods_enum_failure(self):
        """Test private validation methods that raise ValidationError - enum constraints failure."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with invalid enum values
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="INVALID_VALUE"
        )
        reference = GeneralReferenceModule.from_required_elements()
        reference.with_optional_elements(source_image_sequence=[source_item])

        # Enum violations are warnings, not errors, so they won't raise ValidationError
        # This test should pass without raising an exception
        try:
            reference._ensure_enum_constraints_valid()
        except ValidationError:
            pytest.fail("Enum constraint violations should not raise ValidationError (they are warnings)")

    def test_private_validation_methods_sequence_failure(self):
        """Test private validation methods that raise ValidationError - sequence requirements failure."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with invalid sequence structure
        ref_instance_item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[]  # Empty - should cause error
        )
        reference = GeneralReferenceModule.from_required_elements()
        reference.with_optional_elements(referenced_instance_sequence=[ref_instance_item])

        # Should raise ValidationError for sequence requirements
        with pytest.raises(ValidationError) as exc_info:
            reference._ensure_sequence_requirements_valid()

        assert "Sequence requirements validation failed" in str(exc_info.value)

    def test_zero_copy_validation_performance(self):
        """Test that validation methods use zero-copy optimization (pass self instead of dataset)."""
        reference = GeneralReferenceModule.from_required_elements()

        # Add some data to test with
        source_item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved="YES"
        )
        reference.with_optional_elements(source_image_sequence=[source_item])

        # Test all public validation methods (should use zero-copy)
        methods_to_test = [
            reference.validate,
            reference.check_required_elements,
            reference.check_conditional_requirements,
            reference.check_enum_constraints,
            reference.check_sequence_requirements,
        ]

        for method in methods_to_test:
            result = method()
            assert isinstance(result, ValidationResult)
            # Validation should work directly on module without creating Dataset copy
