"""
Test GeneralAcquisitionModule functionality.

GeneralAcquisitionModule implements DICOM PS3.3 C.7.10.1 General Acquisition Module.
All elements are Type 3 (optional).
"""

import pytest
from datetime import datetime, date
from pyrt_dicom.modules import GeneralAcquisitionModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.validators.validation_error import ValidationError


class TestGeneralAcquisitionModule:
    """Test GeneralAcquisitionModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Should be able to create empty instance since all elements are Type 3
        assert acquisition is not None
        assert isinstance(acquisition, GeneralAcquisitionModule)
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000"
        )
        
        # Test via to_dataset() method
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionUID')
        assert hasattr(dataset, 'AcquisitionNumber')
        assert hasattr(dataset, 'AcquisitionDate')
        assert hasattr(dataset, 'AcquisitionTime')
        
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 1
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
    
    def test_with_optional_elements_all(self):
        """Test adding all optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000",
            acquisition_duration=30.5,
            images_in_acquisition=100,
            irradiation_event_uid="*******.*******.9.10.11.13"
        )
        
        # Test via to_dataset() method
        dataset = acquisition.to_dataset()
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 1
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
        assert dataset.AcquisitionDateTime == "20240101120000.000000"
        assert dataset.AcquisitionDuration == 30.5
        assert dataset.ImagesInAcquisition == 100
        assert dataset.IrradiationEventUID == "*******.*******.9.10.11.13"
    
    def test_datetime_formatting(self):
        """Test datetime formatting functionality."""
        now = datetime(2024, 1, 1, 12, 0, 0, 123456)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_datetime=now
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionDateTime')
        assert "20240101120000.123456" in dataset.AcquisitionDateTime
    
    def test_date_formatting(self):
        """Test date formatting functionality."""
        test_date = date(2024, 1, 1)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date=test_date
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionDate')
        assert dataset.AcquisitionDate == "20240101"
    
    def test_time_formatting(self):
        """Test time formatting functionality."""
        test_time = datetime(2024, 1, 1, 14, 30, 45)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_time=test_time
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionTime')
        assert dataset.AcquisitionTime == "143045"
    
    def test_property_methods_empty(self):
        """Test property methods with empty module."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is False
        assert acquisition.has_timing_information is False
        assert acquisition.has_irradiation_event is False
        assert acquisition.has_image_count is False
        
        # Test value properties
        assert acquisition.acquisition_uid_value is None
        assert acquisition.acquisition_number_value is None
        assert acquisition.acquisition_date_value is None
        assert acquisition.acquisition_time_value is None
        assert acquisition.acquisition_datetime_value is None
        assert acquisition.acquisition_duration_value is None
        assert acquisition.images_in_acquisition_value is None
        assert acquisition.irradiation_event_uid_value is None
    
    def test_property_methods_populated(self):
        """Test property methods with populated module."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            acquisition_number=5,
            acquisition_date="20240101",
            acquisition_duration=45.0,
            images_in_acquisition=200,
            irradiation_event_uid="*******.6"
        )
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is True
        assert acquisition.has_timing_information is True
        assert acquisition.has_irradiation_event is True
        assert acquisition.has_image_count is True
        
        # Test value properties
        assert acquisition.acquisition_uid_value == "*******.5"
        assert acquisition.acquisition_number_value == 5
        assert acquisition.acquisition_date_value == "20240101"
        assert acquisition.acquisition_duration_value == 45.0
        assert acquisition.images_in_acquisition_value == 200
        assert acquisition.irradiation_event_uid_value == "*******.6"
    
    def test_acquisition_summary_empty(self):
        """Test acquisition summary with empty module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements()
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert len(summary) == 0
    
    def test_acquisition_summary_populated(self):
        """Test acquisition summary with populated module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            acquisition_number=10,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_duration=60.0,
            images_in_acquisition=150,
            irradiation_event_uid="*******.6"
        )
        
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert hasattr(summary, 'Identification')
        assert hasattr(summary, 'Timing')
        assert hasattr(summary, 'ImagesInAcquisition')
        assert hasattr(summary, 'IrradiationEventUID')
        
        # Check identification dataset
        assert hasattr(summary.Identification, 'AcquisitionUID')
        assert hasattr(summary.Identification, 'AcquisitionNumber')
        assert summary.Identification.AcquisitionUID == "*******.5"
        assert summary.Identification.AcquisitionNumber == 10
        
        # Check timing dataset
        assert hasattr(summary.Timing, 'AcquisitionDate')
        assert hasattr(summary.Timing, 'AcquisitionTime')
        assert hasattr(summary.Timing, 'AcquisitionDuration')
        assert summary.Timing.AcquisitionDate == "20240101"
        assert summary.Timing.AcquisitionTime == "120000"
        assert summary.Timing.AcquisitionDuration == 60.0
        
        # Check direct attributes
        assert summary.ImagesInAcquisition == 150
        assert summary.IrradiationEventUID == "*******.6"
    
    def test_is_synchronized_with_external_clock(self):
        """Test external clock synchronization method."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # This method should always return False as documented
        assert acquisition.is_synchronized_with_external_clock() is False
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        assert hasattr(acquisition, 'validate')
        assert callable(acquisition.validate)
        
        # Test validation result structure
        validation_result = acquisition.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid=None,
            acquisition_number=None,
            acquisition_date=None
        )
        
        # None values should not create attributes in the internal dataset
        dataset = acquisition.to_dataset()
        assert not hasattr(dataset, 'AcquisitionUID')
        assert not hasattr(dataset, 'AcquisitionNumber')
        assert not hasattr(dataset, 'AcquisitionDate')
    
    def test_multiple_irradiation_event_uids(self):
        """Test VM=1-n support for multiple irradiation event UIDs."""
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11",
            "*******.*******.9.12"
        ]
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            irradiation_event_uid=multiple_uids
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'IrradiationEventUID')
        assert acquisition.irradiation_event_uid_value == multiple_uids
        assert acquisition.has_irradiation_event is True
    
    def test_single_irradiation_event_uid(self):
        """Test VM=1-n support for single irradiation event UID."""
        single_uid = "*******.*******.9.10"
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            irradiation_event_uid=single_uid
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'IrradiationEventUID')
        assert acquisition.irradiation_event_uid_value == single_uid
        assert acquisition.has_irradiation_event is True
    
    def test_to_dataset_method(self):
        """Test that to_dataset() method returns proper pydicom Dataset."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=42,
            acquisition_date="20240101",
            acquisition_time="120000"
        )
        
        dataset = acquisition.to_dataset()
        
        # Verify dataset type
        assert isinstance(dataset, Dataset)
        
        # Verify dataset contains expected attributes
        assert hasattr(dataset, 'AcquisitionUID')
        assert hasattr(dataset, 'AcquisitionNumber')
        assert hasattr(dataset, 'AcquisitionDate')
        assert hasattr(dataset, 'AcquisitionTime')
        
        # Verify dataset values are correct
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 42
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
        
        # Verify dataset length
        assert len(dataset) == 4
    
    def test_to_dataset_empty_module(self):
        """Test to_dataset() method with empty module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements()
        dataset = acquisition.to_dataset()
        
        # Should return empty dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) == 0
    
    def test_acquisition_summary_with_multiple_irradiation_uids(self):
        """Test acquisition summary with multiple irradiation event UIDs."""
        from pydicom import Dataset
        
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11"
        ]
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            irradiation_event_uid=multiple_uids
        )
        
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert hasattr(summary, 'IrradiationEventUID')
        assert summary.IrradiationEventUID == multiple_uids
    
    def test_property_methods_with_multiple_irradiation_uids(self):
        """Test property methods with multiple irradiation event UIDs."""
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11"
        ]
        
        acquisition = GeneralAcquisitionModule\
            .from_required_elements()\
            .with_optional_elements(
                irradiation_event_uid=multiple_uids
            )
        
        assert acquisition.has_irradiation_event is True
        uid_value = acquisition.irradiation_event_uid_value
        assert uid_value is not None, "irradiation_event_uid_value should not be None"
        assert uid_value == multiple_uids
        # Verify it's iterable and has the correct length (could be list or MultiValue)
        assert hasattr(uid_value, '__iter__')
        assert len(uid_value) == 2
        # Verify individual values match
        assert list(uid_value) == multiple_uids

    # Tests for new public validation convenience methods with zero-copy optimization

    def test_check_required_elements_empty_module(self):
        """Test check_required_elements with empty module (all Type 3)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        result = acquisition.check_required_elements()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_conditional_requirements_empty_module(self):
        """Test check_conditional_requirements with empty module (all Type 3)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        result = acquisition.check_conditional_requirements()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_enum_constraints_empty_module(self):
        """Test check_enum_constraints with empty module (no enums)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        result = acquisition.check_enum_constraints()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_sequence_requirements_empty_module(self):
        """Test check_sequence_requirements with empty module (no sequences)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        result = acquisition.check_sequence_requirements()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_uid_formats_valid_uids(self):
        """Test check_uid_formats with valid UIDs."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.9",
            irradiation_event_uid="*******.*******.10"
        )

        result = acquisition.check_uid_formats()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_uid_formats_invalid_uids(self):
        """Test check_uid_formats with invalid UIDs."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="invalid-uid-format",
            irradiation_event_uid="another.invalid.uid."
        )

        result = acquisition.check_uid_formats()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2  # Should have errors for both invalid UIDs

    def test_check_datetime_formats_valid_dates(self):
        """Test check_datetime_formats with valid date/time formats."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000"
        )

        result = acquisition.check_datetime_formats()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_datetime_formats_invalid_dates(self):
        """Test check_datetime_formats with invalid date/time formats."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="2024-01-01",  # Wrong format
            acquisition_time="12:00:00",   # Wrong format
            acquisition_datetime="2024-01-01T12:00:00"  # Wrong format
        )

        result = acquisition.check_datetime_formats()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 3  # Should have errors for all invalid formats

    def test_check_numeric_ranges_valid_numbers(self):
        """Test check_numeric_ranges with valid numeric values."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_duration=30.5,
            images_in_acquisition=100
        )

        result = acquisition.check_numeric_ranges()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_numeric_ranges_invalid_numbers(self):
        """Test check_numeric_ranges with invalid numeric values."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_duration=-10.0,  # Negative duration
            images_in_acquisition=-5     # Negative image count
        )

        result = acquisition.check_numeric_ranges()

        assert isinstance(result, ValidationResult)
        # Should have at least one error for negative image count, and warnings for negative duration
        assert len(result.errors) >= 1 or len(result.warnings) >= 1

    def test_check_logical_consistency_consistent_timing(self):
        """Test check_logical_consistency with consistent timing information."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000"
        )

        result = acquisition.check_logical_consistency()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        # Should have no consistency warnings for this scenario

    def test_check_logical_consistency_inconsistent_timing(self):
        """Test check_logical_consistency with inconsistent timing information."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240102120000"  # Different date
        )

        result = acquisition.check_logical_consistency()

        assert isinstance(result, ValidationResult)
        # Should have warnings about inconsistent timing
        assert len(result.warnings) >= 1

    def test_validate_zero_copy_optimization(self):
        """Test that validate method uses zero-copy optimization (passes self)."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.9",
            acquisition_date="20240101"
        )

        # This should work without errors and use zero-copy
        result = acquisition.validate()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    # Tests for private validation methods that raise ValidationError

    def test_ensure_required_elements_valid_always_passes(self):
        """Test _ensure_required_elements_valid always passes (no required elements)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        # Should not raise any exception since all elements are Type 3
        acquisition._ensure_required_elements_valid()

    def test_ensure_conditional_requirements_valid_always_passes(self):
        """Test _ensure_conditional_requirements_valid always passes (no conditional requirements)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        # Should not raise any exception since all elements are Type 3
        acquisition._ensure_conditional_requirements_valid()

    def test_ensure_enum_constraints_valid_always_passes(self):
        """Test _ensure_enum_constraints_valid always passes (no enums)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        # Should not raise any exception since there are no enumerated values
        acquisition._ensure_enum_constraints_valid()

    def test_ensure_sequence_requirements_valid_always_passes(self):
        """Test _ensure_sequence_requirements_valid always passes (no sequences)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        # Should not raise any exception since there are no sequences
        acquisition._ensure_sequence_requirements_valid()

    def test_ensure_uid_formats_valid_with_valid_uids(self):
        """Test _ensure_uid_formats_valid with valid UIDs."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.9",
            irradiation_event_uid="*******.*******.10"
        )

        # Should not raise any exception with valid UIDs
        acquisition._ensure_uid_formats_valid()

    def test_ensure_uid_formats_valid_with_invalid_uids_raises_error(self):
        """Test _ensure_uid_formats_valid raises ValidationError with invalid UIDs."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="invalid-uid-format"
        )

        with pytest.raises(ValidationError) as exc_info:
            acquisition._ensure_uid_formats_valid()

        assert "UID format validation failed" in str(exc_info.value)

    def test_ensure_datetime_formats_valid_with_valid_dates(self):
        """Test _ensure_datetime_formats_valid with valid date/time formats."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000"
        )

        # Should not raise any exception with valid formats
        acquisition._ensure_datetime_formats_valid()

    def test_ensure_datetime_formats_valid_with_invalid_dates_raises_error(self):
        """Test _ensure_datetime_formats_valid raises ValidationError with invalid formats."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="2024-01-01"  # Wrong format
        )

        with pytest.raises(ValidationError) as exc_info:
            acquisition._ensure_datetime_formats_valid()

        assert "Date/time format validation failed" in str(exc_info.value)

    def test_ensure_numeric_ranges_valid_with_valid_numbers(self):
        """Test _ensure_numeric_ranges_valid with valid numeric values."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_duration=30.5,
            images_in_acquisition=100
        )

        # Should not raise any exception with valid numbers
        acquisition._ensure_numeric_ranges_valid()

    def test_ensure_numeric_ranges_valid_with_invalid_numbers_raises_error(self):
        """Test _ensure_numeric_ranges_valid raises ValidationError with invalid numbers."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            images_in_acquisition=-5  # Negative image count (error, not just warning)
        )

        with pytest.raises(ValidationError) as exc_info:
            acquisition._ensure_numeric_ranges_valid()

        assert "Numeric range validation failed" in str(exc_info.value)

    def test_ensure_logical_consistency_valid_with_consistent_data(self):
        """Test _ensure_logical_consistency_valid with consistent data."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000"
        )

        # Should not raise any exception with consistent data
        # Note: logical consistency typically generates warnings, not errors
        acquisition._ensure_logical_consistency_valid()

    # Integration tests for complex scenarios

    def test_validation_integration_with_multiple_errors(self):
        """Test validation integration with multiple validation errors."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="invalid.uid.",  # Invalid UID format
            acquisition_date="2024-01-01",   # Invalid date format
            images_in_acquisition=-10        # Invalid negative count
        )

        result = acquisition.validate()

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 3  # Should have multiple errors

    def test_validation_integration_with_warnings(self):
        """Test validation integration with warnings."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240102120000",  # Inconsistent datetime
            acquisition_duration=-5.0               # Negative duration (warning)
        )

        result = acquisition.validate()

        assert isinstance(result, ValidationResult)
        assert len(result.warnings) >= 2  # Should have warnings for inconsistency and negative duration

    def test_validation_methods_return_validation_result_objects(self):
        """Test that all validation methods return proper ValidationResult objects."""
        acquisition = GeneralAcquisitionModule.from_required_elements()

        methods_to_test = [
            acquisition.check_required_elements,
            acquisition.check_conditional_requirements,
            acquisition.check_enum_constraints,
            acquisition.check_sequence_requirements,
            acquisition.check_uid_formats,
            acquisition.check_datetime_formats,
            acquisition.check_numeric_ranges,
            acquisition.check_logical_consistency,
            acquisition.validate
        ]

        for method in methods_to_test:
            result = method()
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
