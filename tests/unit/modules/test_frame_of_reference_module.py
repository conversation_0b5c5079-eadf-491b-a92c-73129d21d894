"""
Test FrameOfReferenceModule (M - Mandatory) functionality.

FrameOfReferenceModule implements DICOM PS3.3 C.7.4.1 Frame of Reference Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom.uid import generate_uid
from pyrt_dicom.modules import FrameOfReferenceModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.validators.validation_error import ValidationError


class TestFrameOfReferenceModule:
    """Test FrameOfReferenceModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )

        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == frame_uid
    
    def test_frame_uid_uniqueness(self):
        """Test frame of reference UID uniqueness."""
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )

        # UIDs should be different
        dataset1 = frame_ref1.to_dataset()
        dataset2 = frame_ref2.to_dataset()
        assert dataset1.FrameOfReferenceUID != dataset2.FrameOfReferenceUID
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="ISOCENTER"
        )

        dataset = frame_ref.to_dataset()
        assert hasattr(dataset, 'PositionReferenceIndicator')
        assert dataset.PositionReferenceIndicator == "ISOCENTER"
    
    def test_specific_frame_uid_format(self):
        """Test specific frame UID format validation."""
        # Test with specific valid UID
        specific_uid = "1.2.826.0.1.3680043.2.1125.1.12345678901234567890"
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=specific_uid
        )

        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == specific_uid
    
    def test_position_reference_indicators(self):
        """Test various position reference indicator values."""
        indicators = ["ISOCENTER", "COUCH", "EXTERNAL", "PHANTOM"]

        for indicator in indicators:
            frame_ref = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid(),
                position_reference_indicator=indicator
            )
            dataset = frame_ref.to_dataset()
            assert dataset.PositionReferenceIndicator == indicator
    
    def test_frame_of_reference_for_rt_dose(self):
        """Test frame of reference specific to RT dose requirements."""
        # RT Dose typically uses consistent frame of reference with planning CT
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )

        # Verify UID is preserved for spatial consistency
        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == frame_uid
    
    def test_multiple_references_same_frame(self):
        """Test that multiple objects can reference the same frame."""
        shared_frame_uid = generate_uid()

        # Multiple modules using same frame of reference
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )

        # Both should have the same frame UID
        dataset1 = frame_ref1.to_dataset()
        dataset2 = frame_ref2.to_dataset()
        assert dataset1.FrameOfReferenceUID == dataset2.FrameOfReferenceUID
        assert dataset1.FrameOfReferenceUID == shared_frame_uid
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        
        assert hasattr(frame_ref, 'validate')
        assert callable(frame_ref.validate)
        
        # Test validation result structure
        validation_result = frame_ref.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_frame_reference_consistency(self):
        """Test frame reference consistency requirements."""
        # Create frame reference for treatment planning consistency
        planning_frame_uid = generate_uid()

        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=planning_frame_uid
        )

        # Verify consistency for RT dose spatial alignment
        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == planning_frame_uid

    def test_to_dataset_method(self):
        """Test that to_dataset() method works correctly."""
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid,
            position_reference_indicator="STERNAL_NOTCH"
        )

        dataset = frame_ref.to_dataset()
        assert isinstance(dataset, type(frame_ref.to_dataset()))  # Should be pydicom.Dataset
        assert dataset.FrameOfReferenceUID == frame_uid
        assert dataset.PositionReferenceIndicator == "STERNAL_NOTCH"
        assert len(dataset) == 2  # Should have 2 elements

    def test_anatomical_reference_indicators(self):
        """Test anatomical reference indicators helper method."""
        indicators = FrameOfReferenceModule.create_anatomical_reference_indicators()

        assert isinstance(indicators, dict)
        assert "STERNAL_NOTCH" in indicators
        assert "ILIAC_CREST" in indicators
        assert "XIPHOID" in indicators
        assert len(indicators) > 0

    def test_slide_reference_indicator(self):
        """Test slide reference indicator helper method."""
        slide_indicator = FrameOfReferenceModule.create_slide_reference_indicator()
        assert slide_indicator == "SLIDE_CORNER"

    def test_corneal_reference_indicators(self):
        """Test corneal reference indicators helper method."""
        indicators = FrameOfReferenceModule.create_corneal_reference_indicators()

        assert isinstance(indicators, dict)
        assert "CORNEAL_VERTEX_R" in indicators
        assert "CORNEAL_VERTEX_L" in indicators
        assert len(indicators) == 2

    def test_patient_based_frame_properties(self):
        """Test patient-based frame of reference properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        assert frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "PATIENT"
        assert frame_ref.corneal_eye_side is None

    def test_slide_based_frame_properties(self):
        """Test slide-based frame of reference properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="SLIDE_CORNER"
        )

        assert not frame_ref.is_patient_based
        assert frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "SLIDE"
        assert frame_ref.corneal_eye_side is None

    def test_corneal_based_frame_properties(self):
        """Test corneal-based frame of reference properties."""
        # Test right eye
        frame_ref_r = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="CORNEAL_VERTEX_R"
        )

        assert not frame_ref_r.is_patient_based
        assert not frame_ref_r.is_slide_based
        assert frame_ref_r.is_corneal_based
        assert frame_ref_r.has_meaningful_reference
        assert frame_ref_r.reference_type == "CORNEAL"
        assert frame_ref_r.corneal_eye_side == "RIGHT"

        # Test left eye
        frame_ref_l = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="CORNEAL_VERTEX_L"
        )

        assert frame_ref_l.is_corneal_based
        assert frame_ref_l.corneal_eye_side == "LEFT"

    def test_empty_reference_indicator_properties(self):
        """Test frame of reference with empty position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator=""
        )

        assert not frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert not frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "EMPTY"
        assert frame_ref.corneal_eye_side is None

    def test_unknown_reference_indicator_properties(self):
        """Test frame of reference with unknown position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="UNKNOWN_REFERENCE"
        )

        assert not frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "UNKNOWN"
        assert frame_ref.corneal_eye_side is None

    def test_frame_compatibility(self):
        """Test frame of reference compatibility checking."""
        shared_uid = generate_uid()
        different_uid = generate_uid()

        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_uid
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_uid
        )
        frame_ref3 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=different_uid
        )

        # Same UID should be compatible
        assert frame_ref1.is_compatible_with(frame_ref2)
        assert frame_ref2.is_compatible_with(frame_ref1)

        # Different UID should not be compatible
        assert not frame_ref1.is_compatible_with(frame_ref3)
        assert not frame_ref3.is_compatible_with(frame_ref1)

        # Non-FrameOfReferenceModule should not be compatible
        assert not frame_ref1.is_compatible_with("not a module") # type: ignore
        assert not frame_ref1.is_compatible_with(None) # type: ignore

    def test_with_optional_elements_no_args(self):
        """Test with_optional_elements method with no arguments."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )

        # Should return self for chaining
        result = frame_ref.with_optional_elements()
        assert result is frame_ref

    def test_module_properties(self):
        """Test basic module properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        assert frame_ref.module_name == "FrameOfReferenceModule"
        assert frame_ref.has_data
        assert frame_ref.get_element_count() == 2
        assert "FrameOfReferenceModule" in str(frame_ref)
        assert "2 attributes" in str(frame_ref)


class TestFrameOfReferenceModuleValidation:
    """Test new validation integration methods for FrameOfReferenceModule."""

    def test_check_required_elements_success(self):
        """Test check_required_elements with valid data."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        result = frame_ref.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.error_count == 0

    def test_check_required_elements_missing_frame_uid(self):
        """Test check_required_elements with missing Frame of Reference UID."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['PositionReferenceIndicator'] = "STERNAL_NOTCH"

        result = frame_ref.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert result.error_count >= 1
        assert any("Frame of Reference UID" in error for error in result.errors)

    def test_check_required_elements_missing_position_indicator(self):
        """Test check_required_elements with missing Position Reference Indicator."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['FrameOfReferenceUID'] = generate_uid()

        result = frame_ref.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert result.error_count >= 1
        assert any("Position Reference Indicator" in error for error in result.errors)

    def test_check_required_elements_empty_frame_uid(self):
        """Test check_required_elements with empty Frame of Reference UID."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['FrameOfReferenceUID'] = ""
        frame_ref['PositionReferenceIndicator'] = "STERNAL_NOTCH"

        result = frame_ref.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert result.error_count >= 1
        assert any("non-empty" in error for error in result.errors)

    def test_check_enum_constraints_success(self):
        """Test check_enum_constraints with valid enumerated values."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        result = frame_ref.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        # May have warnings for guidance but should not have errors
        assert not result.has_errors

    def test_check_enum_constraints_unknown_indicator(self):
        """Test check_enum_constraints with unknown position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="UNKNOWN_REFERENCE"
        )

        result = frame_ref.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        # Should have warnings for unknown values
        assert result.has_warnings
        assert any("not a standard DICOM value" in warning for warning in result.warnings)

    def test_check_enum_constraints_empty_indicator(self):
        """Test check_enum_constraints with empty position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator=""
        )

        result = frame_ref.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        # Empty is allowed for Type 2, should not have errors or warnings
        assert not result.has_errors
        assert not result.has_warnings

    def test_validate_zero_copy_optimization(self):
        """Test that validate method uses zero-copy optimization."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Test that validate method works
        result = frame_ref.validate()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_with_config(self):
        """Test validate method with custom configuration."""
        from pyrt_dicom.validators.modules.base_validator import ValidationConfig

        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="UNKNOWN_REFERENCE"
        )

        config = ValidationConfig(check_enumerated_values=True)
        result = frame_ref.validate(config)
        assert isinstance(result, ValidationResult)
        assert result.has_warnings  # Should warn about unknown reference


class TestFrameOfReferenceModulePrivateValidation:
    """Test private validation methods that raise ValidationError."""

    def test_ensure_required_elements_valid_success(self):
        """Test _ensure_required_elements_valid with valid data."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Should not raise exception
        frame_ref._ensure_required_elements_valid()

    def test_ensure_required_elements_valid_missing_frame_uid(self):
        """Test _ensure_required_elements_valid with missing Frame of Reference UID."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['PositionReferenceIndicator'] = "STERNAL_NOTCH"

        with pytest.raises(ValidationError) as exc_info:
            frame_ref._ensure_required_elements_valid()

        assert "Required elements validation failed" in str(exc_info.value)
        assert "Frame of Reference UID" in str(exc_info.value)

    def test_ensure_required_elements_valid_missing_position_indicator(self):
        """Test _ensure_required_elements_valid with missing Position Reference Indicator."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['FrameOfReferenceUID'] = generate_uid()

        with pytest.raises(ValidationError) as exc_info:
            frame_ref._ensure_required_elements_valid()

        assert "Required elements validation failed" in str(exc_info.value)
        assert "Position Reference Indicator" in str(exc_info.value)

    def test_ensure_required_elements_valid_empty_frame_uid(self):
        """Test _ensure_required_elements_valid with empty Frame of Reference UID."""
        frame_ref = FrameOfReferenceModule()
        frame_ref['FrameOfReferenceUID'] = ""
        frame_ref['PositionReferenceIndicator'] = "STERNAL_NOTCH"

        with pytest.raises(ValidationError) as exc_info:
            frame_ref._ensure_required_elements_valid()

        assert "Required elements validation failed" in str(exc_info.value)
        assert "non-empty" in str(exc_info.value)

    def test_ensure_enum_constraints_valid_success(self):
        """Test _ensure_enum_constraints_valid with valid data."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Should not raise exception (warnings are not errors)
        frame_ref._ensure_enum_constraints_valid()

    def test_ensure_enum_constraints_valid_unknown_indicator(self):
        """Test _ensure_enum_constraints_valid with unknown position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="UNKNOWN_REFERENCE"
        )

        # Should not raise exception since unknown values generate warnings, not errors
        frame_ref._ensure_enum_constraints_valid()

    def test_ensure_enum_constraints_valid_empty_indicator(self):
        """Test _ensure_enum_constraints_valid with empty position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator=""
        )

        # Should not raise exception since empty is allowed for Type 2
        frame_ref._ensure_enum_constraints_valid()


class TestFrameOfReferenceModuleValidationIntegration:
    """Test integration scenarios for validation methods."""

    def test_validation_methods_return_types(self):
        """Test that all validation methods return correct types."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Public methods should return ValidationResult
        assert isinstance(frame_ref.check_required_elements(), ValidationResult)
        assert isinstance(frame_ref.check_enum_constraints(), ValidationResult)
        assert isinstance(frame_ref.validate(), ValidationResult)

        # Private methods should return None (or raise exception)
        assert frame_ref._ensure_required_elements_valid() is None
        assert frame_ref._ensure_enum_constraints_valid() is None

    def test_validation_consistency(self):
        """Test that public and private validation methods are consistent."""
        # Test with invalid data
        frame_ref = FrameOfReferenceModule()
        frame_ref['PositionReferenceIndicator'] = "STERNAL_NOTCH"
        # Missing FrameOfReferenceUID

        # Public method should return errors
        result = frame_ref.check_required_elements()
        assert result.has_errors

        # Private method should raise exception
        with pytest.raises(ValidationError):
            frame_ref._ensure_required_elements_valid()

    def test_zero_copy_validation_performance(self):
        """Test that validation methods use zero-copy optimization."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        # All validation methods should work without creating dataset copies
        result1 = frame_ref.check_required_elements()
        result2 = frame_ref.check_enum_constraints()
        result3 = frame_ref.validate()

        assert isinstance(result1, ValidationResult)
        assert isinstance(result2, ValidationResult)
        assert isinstance(result3, ValidationResult)

        # Private methods should also work with zero-copy
        frame_ref._ensure_required_elements_valid()
        frame_ref._ensure_enum_constraints_valid()
